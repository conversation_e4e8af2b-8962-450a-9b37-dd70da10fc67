--!strict
--[[
	Gun System Framework - Viewmodel Camera

	Handles first-person camera management:
	- Smooth mouse look
	- FOV transitions
	- Camera bobbing and sway
	- Integration with Roblox camera system
	- Performance optimized updates
]]

local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")
local Workspace = game:GetService("Workspace")

-- ============================================================================
-- VIEWMODEL CAMERA
-- ============================================================================

local ViewmodelCamera = {}
ViewmodelCamera.__index = ViewmodelCamera

-- Constants
local MOUSE_SENSITIVITY = 0.002
local MAX_VERTICAL_ANGLE = math.rad(89)
local MIN_VERTICAL_ANGLE = math.rad(-89)

function ViewmodelCamera.new(config: { [string]: any }): any
  local self = setmetatable({
    -- Configuration
    config = config,

    -- Camera state
    camera = Workspace.CurrentCamera,
    originalCameraType = nil,
    originalFieldOfView = nil,

    -- Rotation tracking
    horizontalAngle = 0,
    verticalAngle = 0,

    -- Character references
    character = nil,
    humanoid = nil,
    rootPart = nil,
    head = nil,

    -- Animation state
    bobTime = 0,
    swayTime = 0,
    recoilOffset = CFrame.new(),

    -- Smoothing
    targetFOV = config.fieldOfView or 70,
    currentFOV = config.fieldOfView or 70,

    -- State
    isEnabled = false,
    isWalking = false,
    walkSpeed = 0,

    -- Tweens
    activeTweens = {},
  }, ViewmodelCamera)

  return self
end

-- ============================================================================
-- CAMERA CONTROL
-- ============================================================================

function ViewmodelCamera:enable(): ()
  if self.isEnabled then
    return
  end

  print("[ViewmodelCamera] Enabling first-person camera")
  self.isEnabled = true

  -- Store original camera settings
  self.originalCameraType = self.camera.CameraType
  self.originalFieldOfView = self.camera.FieldOfView

  -- Set camera to scriptable
  self.camera.CameraType = Enum.CameraType.Scriptable
  self.camera.FieldOfView = self.currentFOV

  -- Initialize rotation from current camera
  local cameraCFrame = self.camera.CFrame
  local lookVector = cameraCFrame.LookVector

  self.horizontalAngle = math.atan2(-lookVector.X, -lookVector.Z)
  self.verticalAngle = math.asin(lookVector.Y)

  -- Start camera update
  self:_startCameraUpdate()
end

function ViewmodelCamera:disable(): ()
  if not self.isEnabled then
    return
  end

  print("[ViewmodelCamera] Disabling first-person camera")
  self.isEnabled = false

  -- Restore original camera settings
  if self.originalCameraType then
    self.camera.CameraType = self.originalCameraType
  end

  if self.originalFieldOfView then
    self.camera.FieldOfView = self.originalFieldOfView
  end

  -- Stop camera update
  self:_stopCameraUpdate()

  -- Cancel active tweens
  for _, tween in pairs(self.activeTweens) do
    tween:Cancel()
  end
  self.activeTweens = {}
end

-- ============================================================================
-- ROTATION HANDLING
-- ============================================================================

function ViewmodelCamera:updateRotation(deltaX: number, deltaY: number): ()
  if not self.isEnabled then
    return
  end

  -- Apply mouse sensitivity
  local sensitivity = MOUSE_SENSITIVITY * (self.config.mouseSensitivity or 1)

  -- Update angles
  self.horizontalAngle -= deltaX * sensitivity
  self.verticalAngle -= deltaY * sensitivity

  -- Clamp vertical angle
  self.verticalAngle = math.clamp(self.verticalAngle, MIN_VERTICAL_ANGLE, MAX_VERTICAL_ANGLE)

  -- Wrap horizontal angle
  if self.horizontalAngle > math.pi then
    self.horizontalAngle -= 2 * math.pi
  elseif self.horizontalAngle < -math.pi then
    self.horizontalAngle += 2 * math.pi
  end
end

function ViewmodelCamera:addRecoil(recoilData: { [string]: any }): ()
  if not self.isEnabled then
    return
  end

  -- Apply recoil to camera rotation
  local verticalRecoil = (recoilData.vertical or 0) * 0.01
  local horizontalRecoil = (recoilData.horizontal or 0) * 0.01

  self.verticalAngle -= verticalRecoil
  self.horizontalAngle += horizontalRecoil * (math.random() > 0.5 and 1 or -1)

  -- Clamp after recoil
  self.verticalAngle = math.clamp(self.verticalAngle, MIN_VERTICAL_ANGLE, MAX_VERTICAL_ANGLE)
end

-- ============================================================================
-- FOV MANAGEMENT
-- ============================================================================

function ViewmodelCamera:setFieldOfView(fov: number, duration: number?): ()
  self.targetFOV = fov

  if duration and duration > 0 then
    -- Smooth FOV transition
    local fovTween = TweenService:Create(
      self,
      TweenInfo.new(duration, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
      { currentFOV = fov }
    )

    -- Cancel existing FOV tween
    if self.activeTweens.fov then
      self.activeTweens.fov:Cancel()
    end

    self.activeTweens.fov = fovTween
    fovTween:Play()

    fovTween.Completed:Connect(function()
      self.activeTweens.fov = nil
    end)
  else
    -- Instant FOV change
    self.currentFOV = fov
  end
end

-- ============================================================================
-- CHARACTER INTEGRATION
-- ============================================================================

function ViewmodelCamera:setCharacter(newCharacter: Model): ()
  self.character = newCharacter
  self.humanoid = newCharacter:WaitForChild("Humanoid")
  self.rootPart = newCharacter:WaitForChild("HumanoidRootPart")
  self.head = newCharacter:WaitForChild("Head")

  -- Connect to humanoid events
  self.humanoid.Running:Connect(function(speed)
    self.walkSpeed = speed
    self.isWalking = speed > 0.1
  end)
end

-- ============================================================================
-- CAMERA EFFECTS
-- ============================================================================

function ViewmodelCamera:_calculateCameraBobbing(deltaTime: number): CFrame
  if not self.config.enableBobbing or not self.isWalking then
    return CFrame.new()
  end

  -- Update bob time based on walk speed
  local bobSpeed = (self.walkSpeed / 16) * 8 -- Normalize to reasonable speed
  self.bobTime += deltaTime * bobSpeed

  -- Calculate bobbing offsets
  local bobIntensity = self.config.bobbingIntensity or 0.02
  local horizontalBob = math.sin(self.bobTime * 2) * bobIntensity * 0.5
  local verticalBob = math.abs(math.sin(self.bobTime)) * bobIntensity

  return CFrame.new(horizontalBob, verticalBob, 0)
end

function ViewmodelCamera:_calculateCameraSway(deltaTime: number): CFrame
  if not self.config.enableSway then
    return CFrame.new()
  end

  -- Update sway time
  self.swayTime += deltaTime

  -- Calculate sway offsets
  local swayIntensity = self.config.swayIntensity or 0.01
  local swayX = math.sin(self.swayTime * 0.7) * swayIntensity
  local swayY = math.cos(self.swayTime * 0.5) * swayIntensity * 0.5

  return CFrame.Angles(math.rad(swayY * 2), math.rad(swayX * 1.5), math.rad(swayX * 0.5))
end

-- ============================================================================
-- CAMERA UPDATE
-- ============================================================================

local cameraConnection = nil

function ViewmodelCamera:_startCameraUpdate(): ()
  if cameraConnection then
    return
  end

  cameraConnection = RunService.Heartbeat:Connect(function(deltaTime)
    self:_updateCamera(deltaTime)
  end)
end

function ViewmodelCamera:_stopCameraUpdate(): ()
  if cameraConnection then
    cameraConnection:Disconnect()
    cameraConnection = nil
  end
end

function ViewmodelCamera:_updateCamera(deltaTime: number): ()
  if not self.isEnabled or not self.rootPart then
    return
  end

  -- Calculate base camera position (head position)
  local headPosition = self.head.Position
  local headOffset = Vector3.new(0, 0, 0) -- Slight offset from head center
  local cameraPosition = headPosition + headOffset

  -- Calculate rotation from angles
  local rotationCFrame = CFrame.Angles(0, self.horizontalAngle, 0)
    * CFrame.Angles(self.verticalAngle, 0, 0)

  -- Apply camera effects
  local bobbingOffset = self:_calculateCameraBobbing(deltaTime)
  local swayOffset = self:_calculateCameraSway(deltaTime)

  -- Combine all offsets
  local finalCFrame = CFrame.new(cameraPosition)
    * rotationCFrame
    * bobbingOffset
    * swayOffset
    * self.recoilOffset

  -- Apply to camera
  self.camera.CFrame = finalCFrame
  self.camera.FieldOfView = self.currentFOV

  -- Update recoil decay
  self:_updateRecoilDecay(deltaTime)
end

function ViewmodelCamera:_updateRecoilDecay(deltaTime: number): ()
  -- Gradually reduce recoil offset
  local decayRate = self.config.recoilDecayRate or 5
  self.recoilOffset = self.recoilOffset:Lerp(CFrame.new(), deltaTime * decayRate)
end

-- ============================================================================
-- CONFIGURATION
-- ============================================================================

function ViewmodelCamera:updateConfig(newConfig: { [string]: any }): ()
  for key, value in pairs(newConfig) do
    self.config[key] = value
  end

  -- Update FOV if changed
  if newConfig.fieldOfView then
    self:setFieldOfView(newConfig.fieldOfView)
  end
end

-- ============================================================================
-- UPDATE METHODS
-- ============================================================================

function ViewmodelCamera:update(): ()
  -- This is called from ViewmodelManager update loop
  -- Most camera updates happen in the Heartbeat connection
  -- This can be used for additional per-frame updates if needed
end

-- ============================================================================
-- UTILITY METHODS
-- ============================================================================

function ViewmodelCamera:getCameraRay(): Ray
  if not self.isEnabled then
    return Ray.new(Vector3.new(), Vector3.new())
  end

  local cameraCFrame = self.camera.CFrame
  return Ray.new(cameraCFrame.Position, cameraCFrame.LookVector * 1000)
end

function ViewmodelCamera:getViewDirection(): Vector3
  if not self.isEnabled then
    return Vector3.new(0, 0, -1)
  end

  return self.camera.CFrame.LookVector
end

function ViewmodelCamera:screenPointToRay(x: number, y: number): Ray
  if not self.isEnabled then
    return Ray.new(Vector3.new(), Vector3.new())
  end

  return self.camera:ScreenPointToRay(x, y)
end

-- ============================================================================
-- CLEANUP
-- ============================================================================

function ViewmodelCamera:cleanup(): ()
  self:disable()

  -- Clear references
  self.character = nil
  self.humanoid = nil
  self.rootPart = nil
  self.head = nil
end

return ViewmodelCamera
