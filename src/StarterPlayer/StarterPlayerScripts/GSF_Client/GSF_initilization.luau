--!strict
--[[
	Gun System Framework - Client Initialization

	This script initializes the GSF client-side systems:
	- Viewmodel system
	- Input handling
	- Integration with GSF core
	- Example weapon setup for testing
]]

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")

-- Wait for GSF to be available
local GSF = require(ReplicatedStorage:WaitForChild("GSF").Core)

-- Viewmodel system
local ViewmodelManager = require(script.ViewmodelManager)

-- ============================================================================
-- CLIENT INITIALIZATION
-- ============================================================================

local player = Players.LocalPlayer :: Player

-- Initialize GSF
print("[GSF_Client] Initializing Gun System Framework...")
GSF.initialize()

-- Configure GSF for FPS
GSF.ConfigurationSystem.setBulkConfiguration({
	viewmodel = {
		enabled = true,
		armsType = "R6", -- Can be "R6", "R15", or "Blocky"
		fieldOfView = 70,
		enableRecoil = true,
		enableSway = true,
	},

	weapon = {
		enableJamming = false,
		enableOverheating = false,
		enableRecoil = true,
		enableSpread = true,
	},

	animations = {
		enableCFrameAnimations = true,
		enableRecoilAnimation = true,
		enableSwayAnimation = true,
		recoilIntensity = 1.0,
		swayIntensity = 0.8,
	},

	effects = {
		enableMuzzleFlash = true,
		enableShellEjection = true,
		effectQuality = "High",
	},

	audio = {
		enableWeaponAudio = true,
		enableSpatialAudio = true,
		weaponVolume = 1.0,
	},
})

-- Initialize viewmodel system
ViewmodelManager.initialize({
	-- Camera settings
	fieldOfView = 70,
	adsFieldOfView = 50,
	mouseSensitivity = 1.0,

	-- Arms settings
	armsType = "R6", -- "R6", "R15", "Blocky"
	armsTransparency = 0,
	hideCharacterInFirstPerson = true,

	-- Weapon settings
	weaponPosition = { X = 0.5, Y = -0.5, Z = -2 },
	weaponRotation = { X = 0, Y = 0, Z = 0 },

	-- Animation settings
	enableRecoil = true,
	enableSway = true,
	enableBobbing = true,
	bobbingIntensity = 0.02,
	swayIntensity = 0.01,

	-- Performance
	updateRate = 60,
})

print("[GSF_Client] Client systems initialized")

-- ============================================================================
-- EXAMPLE WEAPON SETUP
-- ============================================================================

-- Create an example weapon for testing
local function createExampleWeapon(): any
	local weapon = GSF.WeaponEntity.new({
		id = "client_test_m4a1",
		name = "M4A1 Carbine (Client Test)",
		category = "AssaultRifle",

		baseStatistics = {
			damage = 35,
			accuracy = 0.85,
			range = 500,
			fireRate = 700,
			muzzleVelocity = 850,
			recoil = {
				vertical = 0.15,
				horizontal = 0.08,
				pattern = {
					{ X = 0, Y = 0.15, Z = 0 },
					{ X = 0.05, Y = 0.20, Z = 0 },
					{ X = -0.05, Y = 0.25, Z = 0 },
				},
				recovery = 0.8,
			},
		},

		-- Custom animation settings for viewmodel
		animationSettings = {
			recoil = {
				enabled = true,
				verticalIntensity = 0.12,
				horizontalIntensity = 0.06,
				duration = 0.15,
				recoveryTime = 0.3,
				randomness = 0.2,
			},

			sway = {
				enabled = true,
				intensity = 0.02,
				frequency = 1.2,
				breathingEffect = true,
			},

			reload = {
				enabled = true,
				magazineDropTime = 0.8,
				magazineInsertTime = 1.5,
				boltReleaseTime = 2.2,
				smoothing = 0.1,
			},
		},

		attachmentPoints = {
			barrel_mount = {
				name = "barrel_mount",
				position = { X = 0, Y = 0, Z = 0.5 },
				occupied = false,
				component = nil,
			},
			magazine_well = {
				name = "magazine_well",
				position = { X = 0, Y = -0.3, Z = 0.1 },
				occupied = false,
				component = nil,
			},
			optic_rail = {
				name = "optic_rail",
				position = { X = 0, Y = 0.1, Z = -0.2 },
				occupied = false,
				component = nil,
			},
		},
	})

	-- Add components
	local barrel = GSF.createBarrel({
		id = "client_test_barrel",
		name = '14.5" M4 Barrel',
		length = 14.5,
		bore = 5.56,
	})

	local magazine = GSF.createMagazine({
		id = "client_test_magazine",
		name = "30-Round PMAG",
		capacity = 30,
		compatibleAmmo = { "556x45" },
	})

	local sight = GSF.createSight({
		id = "client_test_sight",
		name = "EOTech Holographic Sight",
		magnification = 1.0,
		reticleType = "Holographic",
	})

	-- Attach components
	if barrel then
		weapon:attach(barrel, "barrel_mount")
	end

	if magazine then
		weapon:attach(magazine, "magazine_well")
		magazine:loadAmmunition("556x45", 30)
		weapon:reload(true)
	end

	if sight then
		weapon:attach(sight, "optic_rail")
	end

	return weapon
end

-- ============================================================================
-- INPUT HANDLING
-- ============================================================================

local currentWeapon = nil
local inputConnections = {}

local function setupInputHandling(): ()
	-- Key bindings for testing
	inputConnections.keyInput = UserInputService.InputBegan:Connect(function(input, gameProcessed)
		if gameProcessed then
			return
		end

		if input.KeyCode == Enum.KeyCode.F then
			-- Toggle first person
			ViewmodelManager.toggleFirstPerson()
		elseif input.KeyCode == Enum.KeyCode.E then
			-- Equip/unequip weapon
			if currentWeapon then
				ViewmodelManager.unequipWeapon()
				currentWeapon = nil
				print("[GSF_Client] Weapon unequipped")
			else
				currentWeapon = createExampleWeapon()
				ViewmodelManager.equipWeapon(currentWeapon)
				print("[GSF_Client] Weapon equipped")
			end
		elseif input.KeyCode == Enum.KeyCode.R then
			-- Reload weapon
			if currentWeapon then
				currentWeapon:reload()
				print("[GSF_Client] Reloading weapon")
			end
		elseif input.KeyCode == Enum.KeyCode.T then
			-- Inspect weapon
			if currentWeapon then
				currentWeapon:inspect()
				print("[GSF_Client] Inspecting weapon")
			end
		elseif input.KeyCode == Enum.KeyCode.G then
			-- Test configuration changes
			local newArmsType = ViewmodelManager.getConfig().armsType
			if newArmsType == "R15" then
				newArmsType = "R6"
			elseif newArmsType == "R6" then
				newArmsType = "Blocky"
			else
				newArmsType = "R15"
			end

			ViewmodelManager.setConfiguration({ armsType = newArmsType })
			print(`[GSF_Client] Changed arms type to: {newArmsType}`)
		end
	end)

	-- Mouse input for firing
	inputConnections.mouseInput = UserInputService.InputBegan:Connect(function(input, gameProcessed)
		if gameProcessed then
			return
		end

		if input.UserInputType == Enum.UserInputType.MouseButton1 then
			-- Fire weapon
			if currentWeapon and ViewmodelManager.isInFirstPerson() then
				local camera = workspace.CurrentCamera :: Camera
				local ray = camera:ScreenPointToRay(camera.ViewportSize.X / 2, camera.ViewportSize.Y / 2)

				local fireResult = currentWeapon:fire(ray.Origin, ray.Direction)

				if fireResult.success then
					print("[GSF_Client] Weapon fired successfully")
				else
					print(`[GSF_Client] Fire failed: {fireResult.errorReason}`)
				end
			end
		end
	end)
end

-- ============================================================================
-- STARTUP
-- ============================================================================

-- Wait for character to load
local function onCharacterAdded(character: Model): ()
	-- Character loaded, systems are ready
	print("[GSF_Client] Character loaded, systems ready")

	-- Setup input handling
	setupInputHandling()

	-- Auto-equip weapon for testing
	task.wait(1) -- Wait a moment for everything to settle

	currentWeapon = createExampleWeapon()
	ViewmodelManager.equipWeapon(currentWeapon)

	-- Enter first person automatically
	ViewmodelManager.enterFirstPerson()

	print("[GSF_Client] ===== GSF CLIENT READY =====")
	print("Controls:")
	print("  F - Toggle first person")
	print("  E - Equip/unequip weapon")
	print("  Left Click - Fire weapon")
	print("  Right Click - Aim down sights")
	print("  R - Reload")
	print("  T - Inspect weapon")
	print("  G - Change arms type (R15/R6/Blocky)")
	print("=======================================")
end

-- Connect character events
if player.Character then
	onCharacterAdded(player.Character)
end

player.CharacterAdded:Connect(onCharacterAdded)

-- Cleanup on leaving
player.CharacterRemoving:Connect(function()
	-- Cleanup connections
	for _, connection in pairs(inputConnections) do
		connection:Disconnect()
	end

	-- Cleanup viewmodel
	ViewmodelManager.cleanup()
end)

-- ============================================================================
-- CONFIGURATION TESTING
-- ============================================================================

-- Test different configurations
task.spawn(function()
	task.wait(5) -- Wait 5 seconds after startup

	print("[GSF_Client] Testing configuration system...")

	-- Test performance mode
	task.wait(2)
	GSF.ConfigurationSystem.setPerformanceMode()
	print("[GSF_Client] Switched to Performance mode")

	-- Test high quality mode
	task.wait(3)
	GSF.ConfigurationSystem.setHighQualityMode()
	print("[GSF_Client] Switched to High Quality mode")

	-- Print current configuration
	task.wait(2)
	GSF.ConfigurationSystem.printConfiguration()
end)
