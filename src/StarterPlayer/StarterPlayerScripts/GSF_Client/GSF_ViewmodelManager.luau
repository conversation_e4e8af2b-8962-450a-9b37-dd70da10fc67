--!strict
--[[
	Gun System Framework - Viewmodel Manager

	This is the main controller for the first-person viewmodel system:
	- Manages viewmodel camera and arms
	- Integrates with GSF weapon system
	- Handles first-person animations
	- Supports R6/R15/blocky arms
	- Manages weapon positioning and FOV
]]

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")

-- GSF Integration
local GSF = require(ReplicatedStorage.GSF.Core)

-- Viewmodel Components
local ViewmodelArms = require(script.Parent.GSF_ViewmodelArms)
local ViewmodelCamera = require(script.Parent.GSF_ViewmodelCamera)
local ViewmodelWeapon = require(script.Parent.GSF_ViewmodelWeapon)

-- ============================================================================
-- VIEWMODEL MANAGER
-- ============================================================================

local ViewmodelManager = {}
ViewmodelManager.__index = ViewmodelManager

-- Configuration
local DEFAULT_CONFIG = {
  -- Camera settings
  fieldOfView = 70,
  adsFieldOfView = 50,
  cameraSmoothing = 0.1,

  -- Arms settings
  armsType = "R6", -- "R6", "R15", "Blocky"
  armsTransparency = 0,
  hideCharacterInFirstPerson = true,

  -- Weapon settings
  weaponFOV = 70,
  weaponPosition = { X = 0.5, Y = -0.5, Z = -2 },
  weaponRotation = { X = 0, Y = 0, Z = 0 },

  -- Animation settings
  enableRecoil = true,
  enableSway = true,
  enableBobbing = true,

  -- Performance
  updateRate = 60, -- FPS
  enableLOD = true,
}

-- State
local player = Players.LocalPlayer :: Player
local character = nil

-- Viewmodel components
local viewmodelCamera = nil
local viewmodelArms = nil
local viewmodelWeapon = nil

-- Current state
local currentWeapon = nil
local isFirstPerson = false
local isAiming = false
local config = DEFAULT_CONFIG

-- Input tracking
local mouseMovement = { X = 0, Y = 0 }
local inputConnections = {}

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

function ViewmodelManager.initialize(customConfig: { [string]: any }?): ()
  print("[ViewmodelManager] Initializing viewmodel system...")

  -- Apply custom configuration
  if customConfig then
    for key, value in pairs(customConfig) do
      config[key] = value
    end
  end

  -- Wait for character
  if player.Character then
    ViewmodelManager._onCharacterAdded(player.Character)
  end

  player.CharacterAdded:Connect(ViewmodelManager._onCharacterAdded)
  player.CharacterRemoving:Connect(ViewmodelManager._onCharacterRemoving)

  -- Initialize viewmodel components
  viewmodelCamera = ViewmodelCamera.new(config)
  viewmodelArms = ViewmodelArms.new(config)
  viewmodelWeapon = ViewmodelWeapon.new(config)

  -- Setup input handling
  ViewmodelManager._setupInputHandling()

  -- Connect to GSF configuration system
  if GSF.ConfigurationSystem then
    ViewmodelManager._connectToGSFConfig()
  end

  print("[ViewmodelManager] Viewmodel system initialized")
end

function ViewmodelManager._onCharacterAdded(newCharacter: Model): ()
  character = newCharacter
  humanoid = character:WaitForChild("Humanoid")
  rootPart = character:WaitForChild("HumanoidRootPart")

  -- Initialize viewmodel components with character
  if viewmodelCamera then
    viewmodelCamera:setCharacter(character)
  end

  if viewmodelArms then
    viewmodelArms:setCharacter(character)
  end

  -- Hide character in first person if enabled
  if config.hideCharacterInFirstPerson then
    ViewmodelManager._hideCharacterParts()
  end

  print("[ViewmodelManager] Character loaded:", character.Name)
end

function ViewmodelManager._onCharacterRemoving(): ()
  ViewmodelManager.exitFirstPerson()
  character = nil
end

-- ============================================================================
-- FIRST PERSON CONTROL
-- ============================================================================

function ViewmodelManager.enterFirstPerson(): ()
  if isFirstPerson then
    return
  end

  print("[ViewmodelManager] Entering first person mode")
  isFirstPerson = true

  -- Enable viewmodel camera
  if viewmodelCamera then
    viewmodelCamera:enable()
  end

  -- Show viewmodel arms
  if viewmodelArms then
    viewmodelArms:show()
  end

  -- Show viewmodel weapon if equipped
  if currentWeapon and viewmodelWeapon then
    viewmodelWeapon:show()
  end

  -- Hide character parts
  if config.hideCharacterInFirstPerson then
    ViewmodelManager._hideCharacterParts()
  end

  -- Start update loop
  ViewmodelManager._startUpdateLoop()
end

function ViewmodelManager.exitFirstPerson(): ()
  if not isFirstPerson then
    return
  end

  print("[ViewmodelManager] Exiting first person mode")
  isFirstPerson = false

  -- Disable viewmodel camera
  if viewmodelCamera then
    viewmodelCamera:disable()
  end

  -- Hide viewmodel components
  if viewmodelArms then
    viewmodelArms:hide()
  end

  if viewmodelWeapon then
    viewmodelWeapon:hide()
  end

  -- Show character parts
  ViewmodelManager._showCharacterParts()

  -- Stop update loop
  ViewmodelManager._stopUpdateLoop()
end

function ViewmodelManager.toggleFirstPerson(): ()
  if isFirstPerson then
    ViewmodelManager.exitFirstPerson()
  else
    ViewmodelManager.enterFirstPerson()
  end
end

-- ============================================================================
-- WEAPON MANAGEMENT
-- ============================================================================

function ViewmodelManager.equipWeapon(weapon: any): ()
  print(`[ViewmodelManager] Equipping weapon: {weapon.name}`)

  currentWeapon = weapon

  -- Setup viewmodel weapon
  if viewmodelWeapon then
    viewmodelWeapon:setWeapon(weapon)

    if isFirstPerson then
      viewmodelWeapon:show()
    end
  end

  -- Connect weapon animations to viewmodel
  ViewmodelManager._connectWeaponAnimations(weapon)
end

function ViewmodelManager.unequipWeapon(): ()
  print("[ViewmodelManager] Unequipping weapon")

  if viewmodelWeapon then
    viewmodelWeapon:hide()
    viewmodelWeapon:setWeapon(nil)
  end

  currentWeapon = nil
end

function ViewmodelManager._connectWeaponAnimations(weapon: any): ()
  -- Connect GSF weapon animations to viewmodel
  if weapon.animator then
    -- Override the weapon's animator to use viewmodel
    local originalPlayRecoil = weapon.animator.playRecoilAnimation
    weapon.animator.playRecoilAnimation = function(self, fireData)
      if isFirstPerson and viewmodelWeapon then
        viewmodelWeapon:playRecoilAnimation(fireData)
      else
        originalPlayRecoil(self, fireData)
      end
    end

    local originalPlayReload = weapon.animator.playReloadAnimation
    weapon.animator.playReloadAnimation = function(self, reloadType)
      if isFirstPerson and viewmodelWeapon then
        viewmodelWeapon:playReloadAnimation(reloadType)
      else
        originalPlayReload(self, reloadType)
      end
    end

    local originalPlayInspection = weapon.animator.playInspectionAnimation
    weapon.animator.playInspectionAnimation = function(self)
      if isFirstPerson and viewmodelWeapon then
        viewmodelWeapon:playInspectionAnimation()
      else
        originalPlayInspection(self)
      end
    end
  end
end

-- ============================================================================
-- AIMING SYSTEM
-- ============================================================================

function ViewmodelManager.startAiming(): ()
  if not isFirstPerson or isAiming then
    return
  end

  print("[ViewmodelManager] Starting aim down sights")
  isAiming = true

  -- Adjust camera FOV
  if viewmodelCamera then
    viewmodelCamera:setFieldOfView(config.adsFieldOfView)
  end

  -- Adjust weapon position for ADS
  if viewmodelWeapon then
    viewmodelWeapon:startAiming()
  end
end

function ViewmodelManager.stopAiming(): ()
  if not isAiming then
    return
  end

  print("[ViewmodelManager] Stopping aim down sights")
  isAiming = false

  -- Reset camera FOV
  if viewmodelCamera then
    viewmodelCamera:setFieldOfView(config.fieldOfView)
  end

  -- Reset weapon position
  if viewmodelWeapon then
    viewmodelWeapon:stopAiming()
  end
end

-- ============================================================================
-- INPUT HANDLING
-- ============================================================================

function ViewmodelManager._setupInputHandling(): ()
  -- Mouse movement for camera
  inputConnections.mouseMovement = UserInputService.InputChanged:Connect(
    function(input, gameProcessed)
      if gameProcessed or not isFirstPerson then
        return
      end

      if input.UserInputType == Enum.UserInputType.MouseMovement then
        mouseMovement.X = input.Delta.X
        mouseMovement.Y = input.Delta.Y

        if viewmodelCamera then
          viewmodelCamera:updateRotation(mouseMovement.X, mouseMovement.Y)
        end
      end
    end
  )

  -- Keyboard inputs
  inputConnections.keyboardInput = UserInputService.InputBegan:Connect(
    function(input, gameProcessed)
      if gameProcessed then
        return
      end

      if input.KeyCode == Enum.KeyCode.V then
        -- Toggle first person (default key)
        ViewmodelManager.toggleFirstPerson()
      elseif input.KeyCode == Enum.KeyCode.LeftControl then
        -- Aim down sights
        ViewmodelManager.startAiming()
      end
    end
  )

  inputConnections.keyboardInputEnded = UserInputService.InputEnded:Connect(
    function(input, gameProcessed)
      if gameProcessed then
        return
      end

      if input.KeyCode == Enum.KeyCode.LeftControl then
        -- Stop aiming
        ViewmodelManager.stopAiming()
      end
    end
  )
end

-- ============================================================================
-- UPDATE LOOP
-- ============================================================================

local updateConnection = nil

function ViewmodelManager._startUpdateLoop(): ()
  if updateConnection then
    return
  end

  updateConnection = RunService.Heartbeat:Connect(function(deltaTime)
    ViewmodelManager._update(deltaTime)
  end)
end

function ViewmodelManager._stopUpdateLoop(): ()
  if updateConnection then
    updateConnection:Disconnect()
    updateConnection = nil
  end
end

function ViewmodelManager._update(deltaTime: number): ()
  if not isFirstPerson then
    return
  end

  -- Update viewmodel components
  if viewmodelCamera then
    viewmodelCamera:update(deltaTime)
  end

  if viewmodelArms then
    viewmodelArms:update(deltaTime)
  end

  if viewmodelWeapon then
    viewmodelWeapon:update(deltaTime)
  end
end

-- ============================================================================
-- CHARACTER VISIBILITY
-- ============================================================================

function ViewmodelManager._hideCharacterParts(): ()
  if not character then
    return
  end

  for _, part in pairs(character:GetChildren()) do
    if part:IsA("BasePart") and part.Name ~= "HumanoidRootPart" then
      part.LocalTransparencyModifier = 1
    elseif part:IsA("Accessory") then
      local handle = part:FindFirstChild("Handle")
      if handle then
        handle.LocalTransparencyModifier = 1
      end
    end
  end
end

function ViewmodelManager._showCharacterParts(): ()
  if not character then
    return
  end

  for _, part in pairs(character:GetChildren()) do
    if part:IsA("BasePart") then
      part.LocalTransparencyModifier = 0
    elseif part:IsA("Accessory") then
      local handle = part:FindFirstChild("Handle")
      if handle then
        handle.LocalTransparencyModifier = 0
      end
    end
  end
end

-- ============================================================================
-- CONFIGURATION
-- ============================================================================

function ViewmodelManager._connectToGSFConfig(): ()
  -- Add viewmodel configuration to GSF
  GSF.ConfigurationSystem.setConfiguration("viewmodel", "enabled", true)
  GSF.ConfigurationSystem.setConfiguration("viewmodel", "armsType", config.armsType)
  GSF.ConfigurationSystem.setConfiguration("viewmodel", "fieldOfView", config.fieldOfView)
  GSF.ConfigurationSystem.setConfiguration("viewmodel", "enableRecoil", config.enableRecoil)
end

function ViewmodelManager.setConfiguration(newConfig: { [string]: any }): ()
  for key, value in pairs(newConfig) do
    config[key] = value
  end

  -- Update components with new config
  if viewmodelCamera then
    viewmodelCamera:updateConfig(config)
  end

  if viewmodelArms then
    viewmodelArms:updateConfig(config)
  end

  if viewmodelWeapon then
    viewmodelWeapon:updateConfig(config)
  end
end

-- ============================================================================
-- PUBLIC API
-- ============================================================================

function ViewmodelManager.isInFirstPerson(): boolean
  return isFirstPerson
end

function ViewmodelManager.getCurrentWeapon(): any?
  return currentWeapon
end

function ViewmodelManager.getConfig(): { [string]: any }
  return config
end

-- Cleanup
function ViewmodelManager.cleanup(): ()
  ViewmodelManager.exitFirstPerson()

  for _, connection in pairs(inputConnections) do
    connection:Disconnect()
  end

  if viewmodelCamera then
    viewmodelCamera:cleanup()
  end

  if viewmodelArms then
    viewmodelArms:cleanup()
  end

  if viewmodelWeapon then
    viewmodelWeapon:cleanup()
  end
end

return ViewmodelManager
