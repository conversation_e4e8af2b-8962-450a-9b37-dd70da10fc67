--!strict
--[[
	Gun System Framework - Viewmodel Weapon

	Handles first-person weapon rendering:
	- Weapon model positioning
	- First-person animations
	- Component visualization
	- ADS (Aim Down Sights) transitions
	- Integration with GSF weapon system
]]

local TweenService = game:GetService("TweenService")
local Workspace = game:GetService("Workspace")

-- ============================================================================
-- VIEWMODEL WEAPON
-- ============================================================================

local ViewmodelWeapon = {}
ViewmodelWeapon.__index = ViewmodelWeapon

-- Default weapon positioning
local DEFAULT_WEAPON_POSITION = {
  idle = {
    position = Vector3.new(0.5, -0.5, -2),
    rotation = Vector3.new(0, 0, 0),
  },
  ads = {
    position = Vector3.new(0, -0.2, -1.5),
    rotation = Vector3.new(0, 0, 0),
  },
}

function ViewmodelWeapon.new(config: { [string]: any }): any
  local self = setmetatable({
    -- Configuration
    config = config,

    -- Weapon references
    weapon = nil,
    weaponModel = nil,

    -- Camera reference
    camera = Workspace.CurrentCamera,

    -- Positioning
    basePosition = DEFAULT_WEAPON_POSITION.idle.position,
    baseRotation = DEFAULT_WEAPON_POSITION.idle.rotation,
    currentOffset = CFrame.new(),

    -- Animation state
    recoilOffset = CFrame.new(),
    swayOffset = CFrame.new(),
    reloadOffset = CFrame.new(),
    inspectionOffset = CFrame.new(),
    adsOffset = CFrame.new(),

    -- Animation tracking
    activeAnimations = {},
    swayTime = 0,

    -- State
    isVisible = false,
    isAiming = false,

    -- Tweens
    activeTweens = {},
  }, ViewmodelWeapon)

  return self
end

-- ============================================================================
-- WEAPON MANAGEMENT
-- ============================================================================

function ViewmodelWeapon:setWeapon(newWeapon: any?): ()
  if newWeapon == self.weapon then
    return
  end

  -- Clean up old weapon
  if self.weaponModel then
    self:_destroyWeaponModel()
  end

  self.weapon = newWeapon

  if self.weapon then
    print(`[ViewmodelWeapon] Setting weapon: {self.weapon.name}`)

    -- Create viewmodel weapon model
    self:_createWeaponModel()

    -- Position weapon if visible
    if self.isVisible then
      self:_positionWeapon()
    end
  else
    print("[ViewmodelWeapon] Clearing weapon")
  end
end

function ViewmodelWeapon:show(): ()
  if self.isVisible or not self.weapon then
    return
  end

  print("[ViewmodelWeapon] Showing viewmodel weapon")
  self.isVisible = true

  -- Create weapon model if needed
  if not self.weaponModel then
    self:_createWeaponModel()
  end

  -- Make weapon visible
  if self.weaponModel then
    self:_setWeaponVisibility(true)
    self:_positionWeapon()
  end
end

function ViewmodelWeapon:hide(): ()
  if not self.isVisible then
    return
  end

  print("[ViewmodelWeapon] Hiding viewmodel weapon")
  self.isVisible = false

  -- Hide weapon model
  if self.weaponModel then
    self:_setWeaponVisibility(false)
  end
end

-- ============================================================================
-- WEAPON MODEL CREATION
-- ============================================================================

function ViewmodelWeapon:_createWeaponModel(): ()
  if not self.weapon then
    return
  end

  -- Destroy existing model
  self:_destroyWeaponModel()

  -- Create new weapon model for viewmodel
  self.weaponModel = Instance.new("Model")
  self.weaponModel.Name = `Viewmodel_{self.weapon.name}`
  self.weaponModel.Parent = self.camera

  -- Create weapon parts based on weapon configuration
  self:_createWeaponParts()

  -- Create component models
  self:_createComponentModels()

  -- Set initial visibility
  self:_setWeaponVisibility(self.isVisible)
end

function ViewmodelWeapon:_createWeaponParts(): ()
  if not self.weaponModel then
    return
  end

  -- Create main weapon body (simplified representation)
  local weaponBody = Instance.new("Part")
  weaponBody.Name = "WeaponBody"
  weaponBody.Size = Vector3.new(4, 0.5, 0.2)
  weaponBody.Material = Enum.Material.Metal
  weaponBody.Color = Color3.fromRGB(60, 60, 60)
  weaponBody.Anchored = true
  weaponBody.CanCollide = false
  weaponBody.CastShadow = false
  weaponBody.Parent = self.weaponModel

  -- Create barrel
  local barrel = Instance.new("Part")
  barrel.Name = "Barrel"
  barrel.Size = Vector3.new(2, 0.2, 0.2)
  barrel.Material = Enum.Material.Metal
  barrel.Color = Color3.fromRGB(40, 40, 40)
  barrel.Anchored = true
  barrel.CanCollide = false
  barrel.CastShadow = false
  barrel.Parent = self.weaponModel

  -- Create stock
  local stock = Instance.new("Part")
  stock.Name = "Stock"
  stock.Size = Vector3.new(1.5, 0.3, 0.3)
  stock.Material = Enum.Material.SmoothPlastic
  stock.Color = Color3.fromRGB(80, 60, 40)
  stock.Anchored = true
  stock.CanCollide = false
  stock.CastShadow = false
  stock.Parent = self.weaponModel

  -- Position parts relative to weapon body
  barrel.CFrame = weaponBody.CFrame * CFrame.new(3, 0, 0)
  stock.CFrame = weaponBody.CFrame * CFrame.new(-2.5, 0, 0)
end

function ViewmodelWeapon:_createComponentModels(): ()
  if not self.weapon or not self.weaponModel then
    return
  end

  -- Create visual representations of attached components
  for componentType, component in pairs(self.weapon.components) do
    self:_createComponentModel(componentType, component)
  end
end

function ViewmodelWeapon:_createComponentModel(componentType: string): ()
  if not self.weaponModel then
    return
  end

  -- Create simplified component representation
  local componentPart = Instance.new("Part")
  componentPart.Name = `Component_{componentType}`
  componentPart.Anchored = true
  componentPart.CanCollide = false
  componentPart.CastShadow = false
  componentPart.Parent = self.weaponModel

  -- Configure based on component type
  if componentType == "Sight" then
    componentPart.Size = Vector3.new(0.5, 0.3, 0.8)
    componentPart.Material = Enum.Material.Metal
    componentPart.Color = Color3.fromRGB(30, 30, 30)
    -- Position on top of weapon
    local weaponBody = self.weaponModel:FindFirstChild("WeaponBody")
    if weaponBody then
      componentPart.CFrame = weaponBody.CFrame * CFrame.new(0, 0.4, 0)
    end
  elseif componentType == "Suppressor" then
    componentPart.Size = Vector3.new(1, 0.3, 0.3)
    componentPart.Material = Enum.Material.Metal
    componentPart.Color = Color3.fromRGB(20, 20, 20)
    -- Position on barrel end
    local barrel = self.weaponModel:FindFirstChild("Barrel")
    if barrel then
      componentPart.CFrame = barrel.CFrame * CFrame.new(1.5, 0, 0)
    end
  elseif componentType == "Grip" then
    componentPart.Size = Vector3.new(0.3, 0.8, 0.3)
    componentPart.Material = Enum.Material.SmoothPlastic
    componentPart.Color = Color3.fromRGB(60, 60, 60)
    -- Position under weapon
    local weaponBody = self.weaponModel:FindFirstChild("WeaponBody")
    if weaponBody then
      componentPart.CFrame = weaponBody.CFrame * CFrame.new(1, -0.6, 0)
    end
  end
end

function ViewmodelWeapon:_destroyWeaponModel(): ()
  if self.weaponModel then
    self.weaponModel:Destroy()
    self.weaponModel = nil
  end
end

function ViewmodelWeapon:_setWeaponVisibility(visible: boolean): ()
  if not self.weaponModel then
    return
  end

  for _, part in pairs(self.weaponModel:GetDescendants()) do
    if part:IsA("BasePart") then
      part.Transparency = visible and 0 or 1
    end
  end
end

-- ============================================================================
-- WEAPON POSITIONING
-- ============================================================================

function ViewmodelWeapon:_positionWeapon(): ()
  if not self.weaponModel or not self.camera then
    return
  end

  local cameraCFrame = self.camera.CFrame

  -- Calculate base weapon position
  local basePosition = self.basePosition
  local baseRotation = CFrame.Angles(
    math.rad(self.baseRotation.X),
    math.rad(self.baseRotation.Y),
    math.rad(self.baseRotation.Z)
  )

  -- Combine all offsets
  local finalOffset = self.recoilOffset
    * self.swayOffset
    * self.reloadOffset
    * self.inspectionOffset
    * self.adsOffset

  -- Calculate final weapon CFrame
  local weaponCFrame = cameraCFrame * CFrame.new(basePosition) * baseRotation * finalOffset

  -- Apply to all weapon parts
  local weaponBody = self.weaponModel:FindFirstChild("WeaponBody")
  if weaponBody then
    weaponBody.CFrame = weaponCFrame

    -- Position other parts relative to weapon body
    local barrel = self.weaponModel:FindFirstChild("Barrel")
    if barrel then
      barrel.CFrame = weaponBody.CFrame * CFrame.new(3, 0, 0)
    end

    local stock = self.weaponModel:FindFirstChild("Stock")
    if stock then
      stock.CFrame = weaponBody.CFrame * CFrame.new(-2.5, 0, 0)
    end

    -- Position components
    self:_positionComponents(weaponBody.CFrame)
  end
end

function ViewmodelWeapon:_positionComponents(weaponCFrame: CFrame): ()
  if not self.weaponModel then
    return
  end

  -- Position component models relative to weapon
  for _, part in pairs(self.weaponModel:GetChildren()) do
    if part:IsA("BasePart") and part.Name:find("Component_") then
      local componentType = part.Name:gsub("Component_", "")

      -- Position based on component type
      if componentType == "Sight" then
        part.CFrame = weaponCFrame * CFrame.new(0, 0.4, 0)
      elseif componentType == "Suppressor" then
        part.CFrame = weaponCFrame * CFrame.new(5, 0, 0)
      elseif componentType == "Grip" then
        part.CFrame = weaponCFrame * CFrame.new(1, -0.6, 0)
      end
    end
  end
end

-- ============================================================================
-- ADS (AIM DOWN SIGHTS)
-- ============================================================================

function ViewmodelWeapon:startAiming(): ()
  if self.isAiming then
    return
  end

  print("[ViewmodelWeapon] Starting ADS")
  self.isAiming = true

  -- Transition to ADS position
  local adsPosition = DEFAULT_WEAPON_POSITION.ads.position
  local adsRotation = DEFAULT_WEAPON_POSITION.ads.rotation

  -- Create ADS offset
  local targetADSOffset = CFrame.new(adsPosition - self.basePosition)
    * CFrame.Angles(
      math.rad(adsRotation.X - self.baseRotation.X),
      math.rad(adsRotation.Y - self.baseRotation.Y),
      math.rad(adsRotation.Z - self.baseRotation.Z)
    )

  -- Animate to ADS position
  local adsTween = TweenService:Create(
    self,
    TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
    { adsOffset = targetADSOffset }
  )

  if self.activeTweens.ads then
    self.activeTweens.ads:Cancel()
  end

  self.activeTweens.ads = adsTween
  adsTween:Play()
end

function ViewmodelWeapon:stopAiming(): ()
  if not self.isAiming then
    return
  end

  print("[ViewmodelWeapon] Stopping ADS")
  self.isAiming = false

  -- Animate back to idle position
  local adsTween = TweenService:Create(
    self,
    TweenInfo.new(0.2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
    { adsOffset = CFrame.new() }
  )

  if self.activeTweens.ads then
    self.activeTweens.ads:Cancel()
  end

  self.activeTweens.ads = adsTween
  adsTween:Play()
end

-- ============================================================================
-- ANIMATIONS
-- ============================================================================

function ViewmodelWeapon:playRecoilAnimation(fireData: { [string]: any }): ()
  if not self.config.enableRecoil then
    return
  end

  -- Calculate recoil based on weapon stats
  local recoilIntensity = fireData.recoilIntensity or 1.0
  local verticalRecoil = 0.1 * recoilIntensity
  local horizontalRecoil = 0.05 * recoilIntensity * (math.random() - 0.5) * 2

  -- Create recoil CFrame
  local recoilCFrame = CFrame.Angles(
    math.rad(-verticalRecoil * 10),
    math.rad(horizontalRecoil * 5),
    0
  ) * CFrame.new(0, 0, verticalRecoil * 0.1)

  -- Animate recoil
  local recoilTween = TweenService:Create(
    self,
    TweenInfo.new(0.1, Enum.EasingStyle.Quart, Enum.EasingDirection.Out),
    { recoilOffset = recoilCFrame }
  )

  recoilTween:Play()

  -- Recovery animation
  recoilTween.Completed:Connect(function()
    local recoveryTween = TweenService:Create(
      self,
      TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
      { recoilOffset = CFrame.new() }
    )
    recoveryTween:Play()
  end)
end

function ViewmodelWeapon:playReloadAnimation(reloadType: string): ()
  print(`[ViewmodelWeapon] Playing {reloadType} reload animation`)

  -- Simple reload animation - move weapon down and back up
  local reloadSequence = {
    {
      offset = CFrame.new(0, -0.3, 0) * CFrame.Angles(math.rad(15), 0, 0),
      duration = 0.5,
    },
    {
      offset = CFrame.new(0, -0.5, 0.2) * CFrame.Angles(math.rad(25), 0, 0),
      duration = 1.0,
    },
    {
      offset = CFrame.new(),
      duration = 0.5,
    },
  }

  self:_playAnimationSequence("reload", reloadSequence)
end

function ViewmodelWeapon:playInspectionAnimation(): ()
  print("[ViewmodelWeapon] Playing inspection animation")

  -- Inspection animation - rotate weapon for viewing
  local inspectionCFrame = CFrame.Angles(math.rad(30), math.rad(45), math.rad(15))
    * CFrame.new(0.2, 0, 0.3)

  local inspectionTween = TweenService:Create(
    self,
    TweenInfo.new(2.0, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut, 0, true),
    { inspectionOffset = inspectionCFrame }
  )

  inspectionTween:Play()

  inspectionTween.Completed:Connect(function()
    self.inspectionOffset = CFrame.new()
  end)
end

function ViewmodelWeapon:_playAnimationSequence(sequence: { { [string]: any } }): ()
  local currentStep = 1

  local function playNextStep()
    if currentStep > #sequence then
      self.reloadOffset = CFrame.new()
      return
    end

    local step = sequence[currentStep]
    local tween = TweenService:Create(
      self,
      TweenInfo.new(step.duration, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
      { reloadOffset = step.offset }
    )

    tween:Play()

    tween.Completed:Connect(function()
      currentStep += 1
      playNextStep()
    end)
  end

  playNextStep()
end

-- ============================================================================
-- UPDATE METHODS
-- ============================================================================

function ViewmodelWeapon:update(deltaTime: number): ()
  if not self.isVisible or not self.weaponModel then
    return
  end

  -- Update weapon sway
  self:_updateWeaponSway(deltaTime)

  -- Update weapon position
  self:_positionWeapon()
end

function ViewmodelWeapon:_updateWeaponSway(deltaTime: number): ()
  if not self.config.enableSway then
    return
  end

  self.swayTime += deltaTime * 1.5

  local swayIntensity = 0.01
  local swayX = math.sin(self.swayTime) * swayIntensity
  local swayY = math.cos(self.swayTime * 0.7) * swayIntensity * 0.5

  self.swayOffset = CFrame.Angles(math.rad(swayY * 2), math.rad(swayX * 3), math.rad(swayX * 1))
    * CFrame.new(swayX * 0.02, swayY * 0.02, 0)
end

-- ============================================================================
-- CONFIGURATION
-- ============================================================================

function ViewmodelWeapon:updateConfig(newConfig: { [string]: any }): ()
  for key, value in pairs(newConfig) do
    self.config[key] = value
  end

  -- Update weapon position if changed
  if newConfig.weaponPosition then
    self.basePosition = Vector3.new(
      newConfig.weaponPosition.X,
      newConfig.weaponPosition.Y,
      newConfig.weaponPosition.Z
    )
  end

  if newConfig.weaponRotation then
    self.baseRotation = Vector3.new(
      newConfig.weaponRotation.X,
      newConfig.weaponRotation.Y,
      newConfig.weaponRotation.Z
    )
  end
end

-- ============================================================================
-- CLEANUP
-- ============================================================================

function ViewmodelWeapon:cleanup(): ()
  self:hide()
  self:_destroyWeaponModel()

  -- Cancel active tweens
  for _, tween in pairs(self.activeTweens) do
    tween:Cancel()
  end

  -- Clear references
  self.weapon = nil
  self.camera = nil
end

return ViewmodelWeapon
