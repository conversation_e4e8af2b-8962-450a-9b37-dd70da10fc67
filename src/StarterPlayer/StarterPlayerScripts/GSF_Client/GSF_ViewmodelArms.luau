--!strict
--[[
	Gun System Framework - Viewmodel Arms

	Handles first-person arm rendering:
	- R6/R15/Blocky arm support
	- Arm positioning following weapon animations
	- Integration with weapon-specific animation systems
	- Performance optimized rendering
	- Customizable arm appearance

	Note: Arms follow weapon animations, they do not have independent animations.
	Each weapon's animationSettings control how arms move.
]]

local TweenService = game:GetService("TweenService")
local Workspace = game:GetService("Workspace")

-- ============================================================================
-- VIEWMODEL ARMS
-- ============================================================================

local ViewmodelArms = {}
ViewmodelArms.__index = ViewmodelArms

-- Arm configurations for different character types
local ARM_CONFIGS = {
  R6 = {
    leftArm = {
      size = Vector3.new(1, 2, 1),
      position = Vector3.new(-1.5, 0, -2),
      rotation = Vector3.new(0, 0, 0),
    },
    rightArm = {
      size = Vector3.new(1, 2, 1),
      position = Vector3.new(1.5, 0, -2),
      rotation = Vector3.new(0, 0, 0),
    },
  },

  R15 = {
    leftUpperArm = {
      size = Vector3.new(1, 1.2, 1),
      position = Vector3.new(-1.2, 0.4, -1.8),
      rotation = Vector3.new(0, 0, 0),
    },
    leftLowerArm = {
      size = Vector3.new(1, 1.2, 1),
      position = Vector3.new(-1.2, -0.8, -1.8),
      rotation = Vector3.new(0, 0, 0),
    },
    leftHand = {
      size = Vector3.new(1, 0.4, 1),
      position = Vector3.new(-1.2, -1.6, -1.8),
      rotation = Vector3.new(0, 0, 0),
    },
    rightUpperArm = {
      size = Vector3.new(1, 1.2, 1),
      position = Vector3.new(1.2, 0.4, -1.8),
      rotation = Vector3.new(0, 0, 0),
    },
    rightLowerArm = {
      size = Vector3.new(1, 1.2, 1),
      position = Vector3.new(1.2, -0.8, -1.8),
      rotation = Vector3.new(0, 0, 0),
    },
    rightHand = {
      size = Vector3.new(1, 0.4, 1),
      position = Vector3.new(1.2, -1.6, -1.8),
      rotation = Vector3.new(0, 0, 0),
    },
  },

  Blocky = {
    leftArm = {
      size = Vector3.new(1, 2, 1),
      position = Vector3.new(-1.5, 0, -2),
      rotation = Vector3.new(0, 0, 0),
      material = Enum.Material.SmoothPlastic,
      shape = Enum.PartType.Block,
    },
    rightArm = {
      size = Vector3.new(1, 2, 1),
      position = Vector3.new(1.5, 0, -2),
      rotation = Vector3.new(0, 0, 0),
      material = Enum.Material.SmoothPlastic,
      shape = Enum.PartType.Block,
    },
  },
}

function ViewmodelArms.new(config: { [string]: any }): any
  local self = setmetatable({
    -- Configuration
    config = config,
    armsType = config.armsType or "R6",

    -- Character references
    character = nil,
    humanoid = nil,

    -- Arm models
    armsModel = nil,
    armParts = {},

    -- Camera reference
    camera = Workspace.CurrentCamera,

    -- State
    isVisible = false,

    -- Weapon-driven positioning
    weaponOffset = CFrame.new(), -- Offset from weapon animations
    baseArmPositions = {},

    -- Appearance
    skinColor = Color3.fromRGB(255, 204, 153), -- Default skin color
  }, ViewmodelArms)

  return self
end

-- ============================================================================
-- ARM CREATION
-- ============================================================================

function ViewmodelArms:show(): ()
  if self.isVisible then
    return
  end

  print(`[ViewmodelArms] Showing {self.armsType} arms`)
  self.isVisible = true

  -- Create arms model
  self:_createArmsModel()

  -- Position arms in camera
  self:_positionArms()
end

function ViewmodelArms:hide(): ()
  if not self.isVisible then
    return
  end

  print("[ViewmodelArms] Hiding arms")
  self.isVisible = false

  -- Destroy arms model
  self:_destroyArmsModel()
end

function ViewmodelArms:_createArmsModel(): ()
  -- Clean up existing model
  self:_destroyArmsModel()

  -- Create new arms model
  self.armsModel = Instance.new("Model")
  self.armsModel.Name = "ViewmodelArms"
  self.armsModel.Parent = self.camera

  -- Get arm configuration
  local armConfig = ARM_CONFIGS[self.armsType]
  if not armConfig then
    warn(`[ViewmodelArms] Unknown arms type: {self.armsType}`)
    return
  end

  -- Create arm parts
  for partName, partConfig in pairs(armConfig) do
    local armPart = self:_createArmPart(partName, partConfig)
    self.armParts[partName] = armPart
    self.baseArmPositions[partName] = partConfig.position
  end

  -- Apply character appearance if available
  self:_applyCharacterAppearance()
end

function ViewmodelArms:_createArmPart(partName: string, partConfig: { [string]: any }): Part
  local armPart = Instance.new("Part")
  armPart.Name = partName
  armPart.Size = partConfig.size
  armPart.Material = partConfig.material or Enum.Material.SmoothPlastic
  armPart.Shape = partConfig.shape or Enum.PartType.Block
  armPart.Color = self.skinColor
  armPart.Anchored = true
  armPart.CanCollide = false
  armPart.CastShadow = false
  armPart.Parent = self.armsModel

  -- Set transparency for viewmodel
  armPart.Transparency = self.config.armsTransparency or 0

  return armPart
end

function ViewmodelArms:_destroyArmsModel(): ()
  if self.armsModel then
    self.armsModel:Destroy()
    self.armsModel = nil
  end

  self.armParts = {}
  self.baseArmPositions = {}
end

-- ============================================================================
-- CHARACTER INTEGRATION
-- ============================================================================

function ViewmodelArms:setCharacter(newCharacter: Model): ()
  self.character = newCharacter
  self.humanoid = newCharacter:WaitForChild("Humanoid")

  -- Get character appearance
  self:_extractCharacterAppearance()

  -- Recreate arms with new appearance if visible
  if self.isVisible then
    self:_createArmsModel()
    self:_positionArms()
  end
end

function ViewmodelArms:_extractCharacterAppearance(): ()
  if not self.character then
    return
  end

  -- Get skin color from character
  local bodyColors = self.character:FindFirstChild("Body Colors")
  if bodyColors then
    self.skinColor = bodyColors.LeftArmColor3
  else
    -- Try to get color from existing arm parts
    local leftArm = self.character:FindFirstChild("Left Arm")
      or self.character:FindFirstChild("LeftUpperArm")
    if leftArm then
      self.skinColor = leftArm.Color
    end
  end
end

function ViewmodelArms:_applyCharacterAppearance(): ()
  if not self.character or not self.armsModel then
    return
  end

  -- Apply skin color to all arm parts
  for _, armPart in pairs(self.armParts) do
    armPart.Color = self.skinColor
  end

  -- Apply accessories/clothing if needed
  self:_applyArmAccessories()
end

function ViewmodelArms:_applyArmAccessories(): ()
  -- Apply sleeves, gloves, etc. from character
  for _, accessory in pairs(self.character:GetChildren()) do
    if accessory:IsA("Accessory") then
      local handle = accessory:FindFirstChild("Handle")
      if handle and self:_isArmAccessory(accessory) then
        -- Clone accessory to viewmodel arms
        self:_cloneAccessoryToArms(accessory)
      end
    end
  end
end

function ViewmodelArms:_isArmAccessory(accessory: Accessory): boolean
  -- Check if accessory is arm-related (sleeves, gloves, etc.)
  local accessoryType = accessory:GetAttribute("AccessoryType")
  return accessoryType == "Sleeve"
    or accessoryType == "Glove"
    or (accessory.Name:lower():find("sleeve") ~= nil)
    or (accessory.Name:lower():find("glove") ~= nil)
end

function ViewmodelArms:_cloneAccessoryToArms(accessory: Accessory): ()
  -- Clone and position accessory on viewmodel arms
  local clonedAccessory = accessory:Clone()
  clonedAccessory.Parent = self.armsModel

  -- Note: Basic accessory cloning is implemented. Advanced positioning
  -- based on specific attachment points could be added in the future if needed.
  print("[ViewmodelArms] Applied arm accessory:", accessory.Name)
end

-- ============================================================================
-- ARM POSITIONING
-- ============================================================================

function ViewmodelArms:_positionArms(): ()
  if not self.armsModel or not self.camera then
    return
  end

  local cameraCFrame = self.camera.CFrame

  -- Position each arm part relative to camera with weapon offset
  for partName, armPart in pairs(self.armParts) do
    local basePosition = self.baseArmPositions[partName]
    if basePosition then
      -- Apply weapon offset to base position
      local offsetPosition = self.weaponOffset
        * CFrame.new(basePosition.X, basePosition.Y, basePosition.Z)

      -- Convert to world position
      local worldPosition = cameraCFrame * offsetPosition

      armPart.CFrame = worldPosition
    end
  end
end

-- ============================================================================
-- WEAPON-DRIVEN ARM POSITIONING
-- ============================================================================

function ViewmodelArms:setWeaponOffset(offset: CFrame): ()
  -- Called by weapon animation system to position arms
  self.weaponOffset = offset

  -- Update arm positions immediately if visible
  if self.isVisible then
    self:_positionArms()
  end
end

function ViewmodelArms:animateToOffset(targetOffset: CFrame, duration: number?): ()
  -- Smoothly animate arms to target offset
  local animDuration = duration or 0.2

  local tween = TweenService:Create(
    self,
    TweenInfo.new(animDuration, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
    { weaponOffset = targetOffset }
  )

  -- Update positions during tween
  tween:Play()

  -- Clean up when complete
  tween.Completed:Connect(function()
    if self.isVisible then
      self:_positionArms()
    end
  end)
end

function ViewmodelArms:getWeaponOffset(): CFrame
  -- Returns current weapon-driven offset for arms
  return self.weaponOffset
end

function ViewmodelArms:resetToIdle(): ()
  -- Reset arms to idle position (no weapon offset)
  self.weaponOffset = CFrame.new()

  if self.isVisible then
    self:_positionArms()
  end
end

-- ============================================================================
-- UPDATE METHODS
-- ============================================================================

function ViewmodelArms:update(_deltaTime: number): ()
  if not self.isVisible or not self.armsModel then
    return
  end

  -- Update arm positions to follow camera and weapon offset
  self:_positionArms()
end

-- ============================================================================
-- CONFIGURATION
-- ============================================================================

function ViewmodelArms:updateConfig(newConfig: { [string]: any }): ()
  local oldArmsType = self.armsType

  for key, value in pairs(newConfig) do
    self.config[key] = value
  end

  -- Update arms type if changed
  if newConfig.armsType and newConfig.armsType ~= oldArmsType then
    self.armsType = newConfig.armsType

    if self.isVisible then
      self:_createArmsModel()
      self:_positionArms()
    end
  end

  -- Update transparency if changed
  if newConfig.armsTransparency then
    for _, armPart in pairs(self.armParts) do
      armPart.Transparency = newConfig.armsTransparency
    end
  end
end

-- ============================================================================
-- UTILITY METHODS
-- ============================================================================

function ViewmodelArms:setArmsType(armsType: string): ()
  if armsType == self.armsType then
    return
  end

  self.armsType = armsType

  if self.isVisible then
    self:_createArmsModel()
    self:_positionArms()
  end
end

function ViewmodelArms:setTransparency(transparency: number): ()
  for _, armPart in pairs(self.armParts) do
    armPart.Transparency = transparency
  end
end

function ViewmodelArms:setSkinColor(color: Color3): ()
  self.skinColor = color

  for _, armPart in pairs(self.armParts) do
    armPart.Color = color
  end
end

-- ============================================================================
-- CLEANUP
-- ============================================================================

function ViewmodelArms:cleanup(): ()
  self:hide()

  -- Clear references
  self.character = nil
  self.humanoid = nil
  self.camera = nil
end

return ViewmodelArms
