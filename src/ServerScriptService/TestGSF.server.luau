--!strict
--[[
	Gun System Framework - Test Script
	
	This script runs the complete test suite for the Gun System Framework.
	It demonstrates all the core functionality and validates the architecture.
	
	To run this test:
	1. Make sure this script is in ServerScriptService
	2. Start the game in Studio
	3. Check the output console for test results
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TestWeapon = require(ReplicatedStorage.GSF.Examples.TestWeapon)

-- Wait for game to load
task.wait(2)

print("Starting Gun System Framework Test Suite...")

-- Run the complete test
local success, errorMessage = pcall(function()
	TestWeapon.runCompleteTest()
end)

if not success then
	error(`Test suite failed: {errorMessage}`)
else
	print("Test suite completed successfully!")
end
