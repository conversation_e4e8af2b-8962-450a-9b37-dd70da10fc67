--!strict
--[[
	Test script to verify weapon-specific arm animations are working
]]

local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Wait for GSF to load
local GSF = require(ReplicatedStorage:WaitFor<PERSON>hild("GSF"):WaitF<PERSON><PERSON>hild("Core"))

-- Import the test
local WeaponSpecificArmsTest = require(ReplicatedStorage.GSF.Examples.WeaponSpecificArmsTest)

-- Run the tests
print("🧪 Starting Weapon-Specific Arms Tests...")

WeaponSpecificArmsTest.runWeaponSpecificArmsTest()
WeaponSpecificArmsTest.runIntegrationTest()

print("🎉 All tests completed!")
