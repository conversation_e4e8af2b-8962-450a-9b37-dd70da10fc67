--!strict
--[[
	Gun System Framework - Weapon Type Definitions

	This module contains all weapon-related type definitions including:
	- Weapon entity structure
	- Weapon state management
	- Weapon statistics
	- Fire results and reload mechanics
]]

local Core = require(script.Parent.Core)

-- Import core types
type ComponentType = Core.ComponentType
type WeaponCategory = Core.WeaponCategory
type FireMode = Core.FireMode
type AmmunitionType = Core.AmmunitionType
type AttachmentPoint = Core.AttachmentPoint
type DamageProfile = Core.DamageProfile
type TimingData = Core.TimingData
type Vector3 = Core.Vector3
type ValidationResult = Core.ValidationResult
type AttachResult = Core.AttachResult
type DetachResult = Core.DetachResult

-- ============================================================================
-- WEAPON STATE TYPES
-- ============================================================================

export type WeaponState = {
  -- Ammunition tracking
  ammunition: {
    chambered: number,
    magazine: number,
    reserve: number,
    maxMagazine: number,
    maxReserve: number,
  },

  -- Weapon condition
  condition: {
    durability: number, -- 0.0 to 1.0
    temperature: number, -- Affects performance and reliability
    fouling: number, -- Affects reliability and accuracy
    jamChance: number, -- Current jam probability
  },

  -- Timing information
  timing: TimingData,

  -- Current modes and settings
  modes: {
    fireMode: FireMode,
    safety: boolean,
    aimingDownSights: boolean,
    bipodDeployed: boolean?,
    laserActive: boolean?,
    flashlightActive: boolean?,
  },

  -- Runtime flags
  flags: {
    isReloading: boolean,
    isJammed: boolean,
    isFiring: boolean,
    isInspecting: boolean,
    isOverheated: boolean,
  },
}

export type WeaponStatistics = {
  -- Core performance stats
  damage: DamageProfile,
  accuracy: number, -- Base accuracy (0.0 to 1.0)
  range: number, -- Effective range in studs
  fireRate: number, -- Rounds per minute
  muzzleVelocity: number, -- m/s

  -- Handling characteristics
  recoil: {
    vertical: number,
    horizontal: number,
    pattern: { Vector3 }, -- Recoil pattern points
    recovery: number, -- Recoil recovery speed
  },

  -- Physical properties
  weight: number, -- kg
  length: number, -- cm

  -- Reliability stats
  reliability: number, -- 0.0 to 1.0
  durabilityLoss: number, -- Per shot durability loss
  heatGeneration: number, -- Heat per shot
  coolingRate: number, -- Heat loss per second

  -- Timing stats
  reloadTime: {
    tactical: number, -- Reload with round chambered
    empty: number, -- Reload from empty
    individual: number?, -- For single-shot reloads
  },

  -- Ammunition compatibility
  compatibleAmmo: { AmmunitionType },
  defaultAmmo: AmmunitionType,
}

-- ============================================================================
-- WEAPON ENTITY TYPE
-- ============================================================================

export type Weapon = {
  -- Identity
  id: string,
  name: string,
  category: WeaponCategory,
  version: string,

  -- Component system
  components: { [ComponentType]: WeaponComponent },
  attachmentPoints: { [string]: AttachmentPoint },

  -- State management
  state: WeaponState,
  baseStatistics: WeaponStatistics,
  currentStatistics: WeaponStatistics, -- Modified by components

  -- Runtime properties
  owner: Core.Player?,
  instance: Core.Instance?,
  lastUpdateTime: number,

  -- Core methods
  fire: (self: Weapon, origin: Vector3, direction: Vector3) -> FireResult,
  reload: (self: Weapon, forceEmpty: boolean?) -> ReloadResult,
  inspect: (self: Weapon) -> InspectionResult,

  -- Component management
  attach: (self: Weapon, component: WeaponComponent, point: string) -> AttachResult,
  detach: (self: Weapon, componentType: ComponentType) -> DetachResult,
  getComponent: (self: Weapon, componentType: ComponentType) -> WeaponComponent?,

  -- State management
  updateStatistics: (self: Weapon) -> (),
  validateState: (self: Weapon) -> ValidationResult,

  -- Utility methods
  canFire: (self: Weapon) -> boolean,
  canReload: (self: Weapon) -> boolean,
  getEffectiveRange: (self: Weapon) -> number,
  getAccuracyAtRange: (self: Weapon, range: number) -> number,
}

-- ============================================================================
-- ACTION RESULT TYPES
-- ============================================================================

export type FireResult = {
  success: boolean,
  projectileId: string?,
  muzzleFlash: boolean,
  shellEjected: boolean,

  -- Ballistics data
  origin: Vector3,
  direction: Vector3,
  velocity: number,

  -- State changes
  ammunitionConsumed: number,
  durabilityLoss: number,
  heatGenerated: number,

  -- Effects
  recoilVector: Vector3,
  soundId: string?,
  effectIds: { string }?,

  -- Error information
  errorReason: string?,
}

export type ReloadResult = {
  success: boolean,
  reloadType: "Tactical" | "Empty" | "Individual",
  duration: number,

  -- Ammunition changes
  ammunitionAdded: number,
  reserveConsumed: number,

  -- State changes
  chamberedRound: boolean,

  -- Animation data
  animationId: string?,
  soundIds: { string }?,

  -- Error information
  errorReason: string?,
}

export type InspectionResult = {
  -- Ammunition status
  chamberedRounds: number,
  magazineRounds: number,
  reserveRounds: number,

  -- Condition status
  durabilityPercent: number,
  temperatureLevel: "Cold" | "Normal" | "Warm" | "Hot" | "Overheated",
  foulingLevel: "Clean" | "Light" | "Moderate" | "Heavy" | "Excessive",

  -- Component status
  componentConditions: { [ComponentType]: number },

  -- Malfunction indicators
  jamRisk: "Low" | "Medium" | "High" | "Critical",
  malfunctionWarnings: { string }?,

  -- UI display data
  displayDuration: number,
  animationId: string?,
}

-- ============================================================================
-- WEAPON COMPONENT INTERFACE
-- ============================================================================

export type WeaponComponent = {
  -- Metadata
  id: string,
  type: ComponentType,
  name: string,
  description: string,
  version: string,

  -- Configuration
  config: Core.ComponentConfig,
  stats: Core.ComponentStats,

  -- Dependencies and conflicts
  dependencies: { ComponentType },
  conflicts: { ComponentType },
  requiredAttachmentPoint: string?,

  -- Lifecycle methods
  initialize: (self: WeaponComponent, config: Core.ComponentConfig) -> Core.InitResult,
  validate: (self: WeaponComponent, weapon: Weapon) -> ValidationResult,
  onAttach: (
    self: WeaponComponent,
    weapon: Weapon,
    attachmentPoint: AttachmentPoint
  ) -> AttachResult,
  onDetach: (self: WeaponComponent, weapon: Weapon) -> DetachResult,

  -- Runtime interface
  onFire: (self: WeaponComponent, weapon: Weapon, fireData: FireData) -> (),
  onReload: (self: WeaponComponent, weapon: Weapon, reloadData: ReloadData) -> (),
  onUpdate: (self: WeaponComponent, weapon: Weapon, deltaTime: number) -> (),

  -- Statistics modification
  modifyStatistics: (self: WeaponComponent, baseStats: WeaponStatistics) -> WeaponStatistics,

  -- Component-specific data
  customData: { [string]: any }?,
}

-- ============================================================================
-- EVENT DATA TYPES
-- ============================================================================

export type FireData = {
  weapon: Weapon,
  origin: Vector3,
  direction: Vector3,
  timestamp: number,
  ammunition: AmmunitionType,
  fireMode: FireMode,
}

export type ReloadData = {
  weapon: Weapon,
  reloadType: "Tactical" | "Empty" | "Individual",
  timestamp: number,
  ammunitionType: AmmunitionType,
  roundsToLoad: number,
}

return {}
