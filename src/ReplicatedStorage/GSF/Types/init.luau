--!strict
--[[
	Gun System Framework - Main Types Module

	This module serves as the central export point for all type definitions
	in the Gun System Framework. It provides a clean interface for importing
	types throughout the codebase.

	Usage:
		local Types = require(ReplicatedStorage.GSF.Types)
		local weapon: Types.Weapon = ...
		local projectile: Types.Projectile = ...
]]

-- Import all type modules
local Ballistics = require(script.Ballistics)
local Components = require(script.Components)
local Core = require(script.Core)
local Effects = require(script.Effects)
local Weapon = require(script.Weapon)

-- ============================================================================
-- CORE TYPES EXPORT
-- ============================================================================

-- Enums and constants
export type ComponentType = Core.ComponentType
export type WeaponCategory = Core.WeaponCategory
export type FireMode = Core.FireMode
export type AmmunitionType = Core.AmmunitionType
export type MaterialType = Core.MaterialType
export type RiflingType = Core.RiflingType
export type FeedType = Core.FeedType
export type ReticleType = Core.ReticleType

-- Result and validation types
export type ValidationResult = Core.ValidationResult
export type ValidationError = Core.ValidationError
export type InitResult = Core.InitResult
export type AttachResult = Core.AttachResult
export type DetachResult = Core.DetachResult

-- Math and physics types
export type Vector3 = Core.Vector3
export type CFrame = Core.CFrame
export type Ray = Core.Ray

-- Configuration types
export type ComponentConfig = Core.ComponentConfig
export type ComponentStats = Core.ComponentStats
export type AttachmentPoint = Core.AttachmentPoint
export type DamageProfile = Core.DamageProfile
export type BallisticProperties = Core.BallisticProperties
export type TimingData = Core.TimingData
export type PerformanceMetrics = Core.PerformanceMetrics

-- ============================================================================
-- WEAPON TYPES EXPORT
-- ============================================================================

export type WeaponState = Weapon.WeaponState
export type WeaponStatistics = Weapon.WeaponStatistics
export type Weapon = Weapon.Weapon
export type WeaponComponent = Weapon.WeaponComponent

-- Action results
export type FireResult = Weapon.FireResult
export type ReloadResult = Weapon.ReloadResult
export type InspectionResult = Weapon.InspectionResult

-- Event data
export type FireData = Weapon.FireData
export type ReloadData = Weapon.ReloadData

-- ============================================================================
-- BALLISTICS TYPES EXPORT
-- ============================================================================

export type Projectile = Ballistics.Projectile
export type ProjectileConfig = Ballistics.ProjectileConfig
export type CollisionResult = Ballistics.CollisionResult
export type PenetrationResult = Ballistics.PenetrationResult
export type RicochetResult = Ballistics.RicochetResult
export type Fragment = Ballistics.Fragment
export type MaterialProperties = Ballistics.MaterialProperties
export type EnvironmentalFactors = Ballistics.EnvironmentalFactors
export type TrajectoryData = Ballistics.TrajectoryData
export type TrajectoryUpdate = Ballistics.TrajectoryUpdate

-- System interfaces
export type ProjectileSystem = Ballistics.ProjectileSystem
export type BallisticsCalculator = Ballistics.BallisticsCalculator

-- ============================================================================
-- EFFECTS TYPES EXPORT
-- ============================================================================

export type EffectType = Effects.EffectType
export type Effect = Effects.Effect
export type MuzzleFlashData = Effects.MuzzleFlashData
export type ShellEjectionData = Effects.ShellEjectionData
export type ImpactEffectData = Effects.ImpactEffectData

-- Audio types
export type AudioType = Effects.AudioType
export type AudioInstance = Effects.AudioInstance
export type AcousticEnvironment = Effects.AcousticEnvironment
export type TinnitusEffect = Effects.TinnitusEffect

-- Event system types
export type EventType = Effects.EventType
export type GameEvent = Effects.GameEvent
export type EventHandler = Effects.EventHandler
export type EventBus = Effects.EventBus

-- UI and feedback types
export type UIFeedbackType = Effects.UIFeedbackType
export type HitMarkerData = Effects.HitMarkerData
export type DamageIndicatorData = Effects.DamageIndicatorData
export type CrosshairData = Effects.CrosshairData

-- System interfaces
export type EffectSystem = Effects.EffectSystem
export type AudioSystem = Effects.AudioSystem

-- ============================================================================
-- COMPONENT TYPES EXPORT
-- ============================================================================

export type BarrelComponent = Components.BarrelComponent
export type SightComponent = Components.SightComponent
export type MagazineComponent = Components.MagazineComponent
export type StockComponent = Components.StockComponent
export type GripComponent = Components.GripComponent
export type TriggerComponent = Components.TriggerComponent
export type SuppressorComponent = Components.SuppressorComponent
export type LaserLightComponent = Components.LaserLightComponent

-- Component factory types
export type ComponentFactoryFunction<T> = Components.ComponentFactoryFunction<T>
export type ComponentFactory = Components.ComponentFactory

-- ============================================================================
-- UTILITY TYPES
-- ============================================================================

-- Common Roblox types that we reference
export type Player = Player
export type Instance = Instance
export type Model = Model
export type Part = Part
export type Tool = Tool
export type RemoteEvent = RemoteEvent
export type RemoteFunction = RemoteFunction
export type BindableEvent = BindableEvent
export type BindableFunction = BindableFunction

-- Color and visual types
export type Color3 = Color3
export type BrickColor = BrickColor
export type Material = Enum.Material

-- ============================================================================
-- TYPE GUARDS AND UTILITIES
-- ============================================================================

-- Type guard functions for runtime type checking
local TypeGuards = {
  isWeapon = function(obj: any): boolean
    return type(obj) == "table"
      and type(obj.id) == "string"
      and type(obj.name) == "string"
      and type(obj.category) == "string"
      and type(obj.fire) == "function"
      and type(obj.reload) == "function"
  end,

  isWeaponComponent = function(obj: any): boolean
    return type(obj) == "table"
      and type(obj.id) == "string"
      and type(obj.type) == "string"
      and type(obj.initialize) == "function"
      and type(obj.onAttach) == "function"
      and type(obj.onDetach) == "function"
  end,

  isProjectile = function(obj: any): boolean
    return type(obj) == "table"
      and type(obj.id) == "string"
      and type(obj.position) == "table"
      and type(obj.velocity) == "table"
      and type(obj.mass) == "number"
      and type(obj.isActive) == "boolean"
  end,

  isValidComponentType = function(componentType: string): boolean
    local validTypes = {
      "Barrel",
      "Stock",
      "Grip",
      "Sight",
      "Magazine",
      "Trigger",
      "Bolt",
      "Compensator",
      "Laser",
      "Light",
      "Suppressor",
      "Bipod",
      "Foregrip",
      "Scope",
    }

    for _, validType in validTypes do
      if componentType == validType then
        return true
      end
    end
    return false
  end,

  isValidFireMode = function(fireMode: string): boolean
    return fireMode == "Semi" or fireMode == "Auto" or fireMode == "Burst" or fireMode == "Safe"
  end,

  isValidWeaponCategory = function(category: string): boolean
    local validCategories = {
      "AssaultRifle",
      "SniperRifle",
      "Shotgun",
      "Pistol",
      "SubmachineGun",
      "LightMachineGun",
      "MarksmanRifle",
    }

    for _, validCategory in validCategories do
      if category == validCategory then
        return true
      end
    end
    return false
  end,
}

-- ============================================================================
-- MODULE EXPORTS
-- ============================================================================

return {
  -- Export type guard utilities
  TypeGuards = TypeGuards,

  -- Version information
  VERSION = "1.0.0",
  API_VERSION = "1.0",

  -- Type documentation
  DOCUMENTATION = {
    description = "Gun System Framework Type Definitions",
    author = "Dynamic Innovative Studio",
    license = "COMMERCIAL",
    repository = "https://github.com/Dynamic-Innovative-Studio/Site-112.git",
  },
}
