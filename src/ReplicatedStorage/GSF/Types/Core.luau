--!strict
--[[
	Gun System Framework - Core Type Definitions

	This module contains the fundamental type definitions for the entire gun system framework.
	All types are strictly typed using Luau's type system for maximum safety and performance.

	Based on the technical design document requirements for:
	- Component-based weapon architecture
	- Strict type safety
	- Modular design patterns
	- Performance optimization
]]

-- ============================================================================
-- CORE ENUMS AND CONSTANTS
-- ============================================================================

export type ComponentType =
  "Barrel"
  | "Stock"
  | "Grip"
  | "Sight"
  | "Magazine"
  | "Trigger"
  | "Bolt"
  | "Compensator"
  | "Laser"
  | "Light"
  | "Suppressor"
  | "Bipod"
  | "Foregrip"
  | "Scope"

export type WeaponCategory =
  "AssaultRifle"
  | "SniperRifle"
  | "Shotgun"
  | "Pistol"
  | "SubmachineGun"
  | "LightMachineGun"
  | "MarksmanRifle"

export type FireMode = "Semi" | "Auto" | "Burst" | "Safe"

export type AmmunitionType =
  "556x45"
  | "762x39"
  | "762x51"
  | "9x19"
  | "45ACP"
  | "12Gauge"
  | "338Lapua"
  | "50BMG"
  | "300Blackout"

export type MaterialType =
  "Steel"
  | "Aluminum"
  | "Polymer"
  | "Wood"
  | "Carbon"
  | "Concrete"
  | "Brick"
  | "Glass"
  | "Flesh"
  | "Kevlar"

export type RiflingType = "Standard" | "Polygonal" | "Smoothbore"
export type FeedType = "Magazine" | "Belt" | "Clip" | "Single"
export type ReticleType = "Dot" | "Cross" | "Mil-Dot" | "ACOG" | "Holographic"

-- ============================================================================
-- RESULT AND VALIDATION TYPES
-- ============================================================================

export type ValidationResult = {
  isValid: boolean,
  errorCode: ValidationError?,
  severity: "Warning" | "Error" | "Critical",
  description: string,
  suggestedAction: "Allow" | "Correct" | "Reject" | "Flag",
}

export type ValidationError =
  "InvalidFireRate"
  | "ImpossibleReloadTime"
  | "InvalidHitRegistration"
  | "StatisticallyImprobable"
  | "PhysicsViolation"
  | "WeaponMismatch"
  | "ComponentIncompatible"
  | "ConfigurationInvalid"
  | "DependencyMissing"

export type InitResult = {
  success: boolean,
  message: string,
  warnings: { string }?,
}

export type AttachResult = {
  success: boolean,
  attachmentPoint: string?,
  modifiedStats: any?, -- todo: Will be properly typed later
  conflicts: { ComponentType }?,
}

export type DetachResult = {
  success: boolean,
  restoredStats: any?, -- todo: Will be properly typed later
  dependentComponents: { ComponentType }?,
}

-- ============================================================================
-- BASIC MATH AND PHYSICS TYPES
-- ============================================================================

export type Vector3 = {
  X: number,
  Y: number,
  Z: number,
}

export type CFrame = {
  Position: Vector3,
  LookVector: Vector3,
  RightVector: Vector3,
  UpVector: Vector3,
}

export type Ray = {
  Origin: Vector3,
  Direction: Vector3,
}

-- ============================================================================
-- COMPONENT CONFIGURATION TYPES
-- ============================================================================

export type ComponentConfig = {
  -- Basic properties
  id: string,
  name: string,
  description: string?,

  -- Physical properties
  mass: number?,
  length: number?,
  width: number?,
  height: number?,

  -- Material properties
  material: MaterialType?,
  durability: number?, -- 0.0 to 1.0

  -- Performance modifiers
  accuracyModifier: number?,
  recoilModifier: number?,
  rangeModifier: number?,
  damageModifier: number?,

  -- Custom properties (component-specific)
  customProperties: { [string]: any }?,
}

export type ComponentStats = {
  -- Performance impact
  accuracyBonus: number,
  recoilReduction: number,
  rangeIncrease: number,
  damageMultiplier: number,

  -- Physical properties
  massAddition: number,
  lengthAddition: number,

  -- Reliability impact
  durabilityModifier: number,
  jamChanceModifier: number,

  -- Special effects
  muzzleFlashReduction: number?,
  soundSuppressionLevel: number?,
  stabilizationBonus: number?,
}

export type AttachmentPoint = {
  name: string,
  position: Vector3,
  orientation: Vector3,
  compatibleTypes: { ComponentType },
  occupied: boolean,
  component: any?, -- todo: Will be WeaponComponent when defined
}

-- ============================================================================
-- AMMUNITION AND BALLISTICS TYPES
-- ============================================================================

export type DamageProfile = {
  baseDamage: number,
  headMultiplier: number,
  chestMultiplier: number,
  limbMultiplier: number,
  falloffStart: number, -- Distance in studs
  falloffEnd: number,
  minimumDamage: number,
}

export type BallisticProperties = {
  muzzleVelocity: number, -- m/s
  mass: number, -- grams
  diameter: number, -- mm
  coefficientOfDrag: number,
  ballisticCoefficient: number,
  penetrationPower: number,
  fragmentationChance: number, -- 0.0 to 1.0
}

-- ============================================================================
-- TIMING AND PERFORMANCE TYPES
-- ============================================================================

export type TimingData = {
  lastFired: number,
  reloadStarted: number?,
  cycleTime: number,
  cooldownEnd: number?,
}

export type PerformanceMetrics = {
  frameRate: number,
  memoryUsage: number,
  networkLatency: number,
  cpuUsage: number,
  activeProjectiles: number,
  activeEffects: number,
}

-- ============================================================================
-- FORWARD DECLARATIONS
-- ============================================================================

-- todo: These will be properly defined in their respective modules
export type Weapon = any
export type WeaponComponent = any
export type Projectile = any
export type Effect = any
export type Player = any
export type Instance = any

return {}
