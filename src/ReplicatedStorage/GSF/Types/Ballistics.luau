--!strict
--[[
	Gun System Framework - Ballistics Type Definitions

	This module contains all ballistics and projectile-related type definitions including:
	- Projectile physics and properties
	- Collision detection and penetration
	- Environmental factors
	- Trajectory calculations
]]

local Core = require(script.Parent.Core)

-- Import core types
type AmmunitionType = Core.AmmunitionType
type MaterialType = Core.MaterialType
type Vector3 = Core.Vector3
type Ray = Core.Ray
type DamageProfile = Core.DamageProfile
type BallisticProperties = Core.BallisticProperties

-- ============================================================================
-- PROJECTILE TYPES
-- ============================================================================

export type Projectile = {
  -- Identity
  id: string,
  ammunition: AmmunitionType,
  sourceWeaponId: string,
  ownerId: number?, -- Player UserId

  -- Physics properties
  position: Vector3,
  velocity: Vector3,
  mass: number, -- grams
  diameter: number, -- mm
  coefficientOfDrag: number,
  ballisticCoefficient: number,

  -- State tracking
  energy: number, -- Kinetic energy in joules
  spin: number, -- RPM for Magnus effect
  timeAlive: number, -- seconds
  distanceTraveled: number, -- studs
  hasRicocheted: boolean,
  penetrationCount: number,

  -- Damage properties
  damage: DamageProfile,
  penetrationPower: number,
  fragmentationChance: number, -- 0.0 to 1.0

  -- Environmental tracking
  lastPosition: Vector3,
  trajectory: { Vector3 }, -- Position history for debugging

  -- Lifecycle flags
  isActive: boolean,
  shouldDestroy: boolean,
  destroyReason: string?,
}

export type ProjectileConfig = {
  ammunition: AmmunitionType,
  ballistics: BallisticProperties,
  damage: DamageProfile,

  -- Physics settings
  gravityMultiplier: number?,
  airResistanceMultiplier: number?,
  enableMagnusEffect: boolean?,

  -- Simulation settings
  maxLifetime: number?, -- seconds
  maxDistance: number?, -- studs
  minVelocity: number?, -- m/s before destruction

  -- Visual settings
  tracerEnabled: boolean?,
  tracerColor: Color3?,
  tracerLength: number?,
}

-- ============================================================================
-- COLLISION AND PENETRATION TYPES
-- ============================================================================

export type CollisionResult = {
  hit: boolean,
  position: Vector3,
  normal: Vector3,
  distance: number,

  -- Hit object information
  instance: Core.Instance?,
  material: MaterialType,
  thickness: number?, -- For penetration calculations

  -- Hit entity information (if applicable)
  hitPlayer: Core.Player?,
  hitBodyPart: string?,

  -- Collision properties
  impactAngle: number, -- degrees from normal
  impactVelocity: number, -- m/s
  impactEnergy: number, -- joules
}

export type PenetrationResult = {
  canPenetrate: boolean,
  penetrationDepth: number, -- studs
  energyLoss: number, -- joules
  velocityLoss: number, -- m/s
  deflectionAngle: number, -- degrees

  -- Post-penetration properties
  exitVelocity: Vector3?,
  exitPosition: Vector3?,
  fragmentationOccurred: boolean,
  fragments: { Fragment }?,
}

export type RicochetResult = {
  willRicochet: boolean,
  newDirection: Vector3?,
  energyRetained: number, -- 0.0 to 1.0
  velocityRetained: number, -- 0.0 to 1.0

  -- Ricochet properties
  ricochetAngle: number, -- degrees
  surfaceFriction: number,
  soundIntensity: number, -- For audio effects
}

export type Fragment = {
  position: Vector3,
  velocity: Vector3,
  mass: number, -- grams
  energy: number, -- joules
  damage: number,
  lifetime: number, -- seconds
}

-- ============================================================================
-- MATERIAL PROPERTIES
-- ============================================================================

export type MaterialProperties = {
  name: MaterialType,
  density: number, -- kg/m³
  hardness: number, -- 0.0 to 1.0
  elasticity: number, -- 0.0 to 1.0
  friction: number, -- 0.0 to 1.0

  -- Penetration resistance
  penetrationResistance: number,
  spallationFactor: number, -- Fragment generation

  -- Ricochet properties
  ricochetThreshold: number, -- minimum angle for ricochet
  ricochetCoefficient: number, -- energy retention

  -- Sound properties
  impactSoundId: string?,
  penetrationSoundId: string?,
  ricochetSoundId: string?,
}

-- ============================================================================
-- ENVIRONMENTAL FACTORS
-- ============================================================================

export type EnvironmentalFactors = {
  -- Atmospheric conditions
  temperature: number, -- Celsius
  pressure: number, -- kPa
  humidity: number, -- 0.0 to 1.0

  -- Wind conditions
  windVelocity: Vector3, -- m/s
  windGustFactor: number, -- 0.0 to 1.0

  -- Gravity and physics
  gravity: Vector3, -- m/s²
  airDensity: number, -- kg/m³

  -- Visibility and lighting
  visibility: number, -- 0.0 to 1.0
  lightLevel: number, -- 0.0 to 1.0
}

export type TrajectoryData = {
  points: { Vector3 },
  velocities: { number },
  energies: { number },
  timeStamps: { number },

  -- Calculated properties
  maxHeight: number,
  totalDistance: number,
  flightTime: number,
  impactVelocity: number,
  impactAngle: number,
}

-- ============================================================================
-- BALLISTICS SYSTEM INTERFACES
-- ============================================================================

export type ProjectileSystem = {
  -- Core physics simulation
  createProjectile: (config: ProjectileConfig, origin: Vector3, velocity: Vector3) -> Projectile,
  updateProjectile: (projectile: Projectile, deltaTime: number) -> TrajectoryUpdate,
  destroyProjectile: (projectile: Projectile, reason: string) -> (),

  -- Collision detection
  checkCollisions: (projectile: Projectile, trajectory: Ray) -> { CollisionResult },
  calculatePenetration: (projectile: Projectile, collision: CollisionResult) -> PenetrationResult,
  calculateRicochet: (projectile: Projectile, collision: CollisionResult) -> RicochetResult,

  -- Environmental effects
  applyGravity: (projectile: Projectile, deltaTime: number) -> Vector3,
  applyAirResistance: (projectile: Projectile, environment: EnvironmentalFactors) -> Vector3,
  applyMagnusEffect: (projectile: Projectile) -> Vector3,
  applyWindEffect: (projectile: Projectile, environment: EnvironmentalFactors) -> Vector3,

  -- Utility functions
  getActiveProjectiles: () -> { Projectile },
  getProjectileCount: () -> number,
  cleanupExpiredProjectiles: () -> number,
}

export type BallisticsCalculator = {
  -- Trajectory calculations
  calculateTrajectory: (
    muzzleVelocity: number,
    launchAngle: number,
    environment: EnvironmentalFactors,
    projectileConfig: ProjectileConfig
  ) -> TrajectoryData,

  -- Energy calculations
  calculateKineticEnergy: (mass: number, velocity: number) -> number,
  calculateEnergyAtDistance: (
    initialEnergy: number,
    distance: number,
    dragCoefficient: number,
    environment: EnvironmentalFactors
  ) -> number,

  -- Penetration mechanics
  calculatePenetrationDepth: (
    projectile: Projectile,
    material: MaterialProperties,
    impactAngle: number
  ) -> number,
  calculatePostPenetrationVelocity: (
    projectile: Projectile,
    penetrationResult: PenetrationResult
  ) -> Vector3,

  -- Environmental effects
  calculateWindDeflection: (
    projectile: Projectile,
    windVector: Vector3,
    timeOfFlight: number
  ) -> Vector3,
  calculateCoriolisEffect: (
    projectile: Projectile,
    latitude: number,
    azimuth: number,
    timeOfFlight: number
  ) -> Vector3,

  -- Utility calculations
  calculateTimeOfFlight: (distance: number, muzzleVelocity: number, angle: number) -> number,
  calculateBulletDrop: (distance: number, muzzleVelocity: number, gravity: number) -> number,
  calculateLeadAngle: (
    targetVelocity: Vector3,
    projectileVelocity: number,
    distance: number
  ) -> number,
}

export type TrajectoryUpdate = {
  newPosition: Vector3,
  newVelocity: Vector3,
  energyLoss: number,
  collisions: { CollisionResult },
  shouldContinue: boolean,
}

return {}
