--!strict
--[[
	Gun System Framework - Grip Component Factory
	
	This module implements a factory for creating grip components.
	Grips affect weapon handling, control speed, and ergonomics.
	Supports various grip types including vertical, angled, and hand stops.
]]

local Types = require(script.Parent.Parent.Types)

-- Type imports
type GripComponent = Types.GripComponent
type ComponentConfig = Types.ComponentConfig
type WeaponStatistics = Types.WeaponStatistics
type Weapon = Types.Weapon
type AttachmentPoint = Types.AttachmentPoint
type ValidationResult = Types.ValidationResult
type InitResult = Types.InitResult
type AttachResult = Types.AttachResult
type DetachResult = Types.DetachResult
type FireData = Types.FireData
type ReloadData = Types.ReloadData

-- ============================================================================
-- GRIP COMPONENT IMPLEMENTATION
-- ============================================================================

local function createGripComponent(config: ComponentConfig): GripComponent
	-- Validate grip-specific configuration
	assert(config.customProperties and config.customProperties.gripType, "Grip type is required")
	
	local grip: GripComponent = {
		-- Base component properties
		id = config.id,
		type = "Grip",
		name = config.name,
		description = config.description or "Weapon grip component",
		version = "1.0.0",
		
		-- Configuration and stats
		config = config,
		stats = {
			accuracyBonus = config.accuracyModifier or 0,
			recoilReduction = config.recoilModifier or 0.08, -- 8% recoil reduction
			rangeIncrease = 0,
			damageMultiplier = 1,
			massAddition = config.mass or 0.15,
			lengthAddition = 0,
			durabilityModifier = 1.0,
			jamChanceModifier = 0,
			muzzleFlashReduction = 0,
			soundSuppressionLevel = 0,
			stabilizationBonus = config.customProperties.stabilizationBonus or 0.15,
		},
		
		-- Dependencies and conflicts
		dependencies = {},
		conflicts = {"Grip", "Foregrip"}, -- Only one grip per weapon
		requiredAttachmentPoint = "foregrip_rail",
		
		-- Grip-specific properties
		gripType = config.customProperties.gripType,
		gripAngle = config.customProperties.gripAngle or 90, -- degrees
		textureType = config.customProperties.textureType or "Stippled",
		
		-- Ergonomics
		palmSwell = config.customProperties.palmSwell or false,
		fingerGrooves = config.customProperties.fingerGrooves or false,
		thumbRest = config.customProperties.thumbRest or false,
		
		-- Grip state
		wearPattern = nil, -- Areas of wear
		gripTape = config.customProperties.gripTape or false,
		
		-- Custom data
		customData = config.customProperties,
		
		-- ============================================================================
		-- LIFECYCLE METHODS
		-- ============================================================================
		
		initialize = function(self: GripComponent, initConfig: ComponentConfig): InitResult
			if self.gripAngle < 0 or self.gripAngle > 180 then
				return {
					success = false,
					message = "Grip angle must be between 0 and 180 degrees",
					warnings = {}
				}
			end
			
			local warnings = {}
			
			-- Check for unusual configurations
			if self.gripAngle < 45 then
				table.insert(warnings, "Very acute grip angle may affect ergonomics")
			end
			
			if self.gripAngle > 135 then
				table.insert(warnings, "Very obtuse grip angle may affect control")
			end
			
			return {
				success = true,
				message = `Grip component {self.id} initialized successfully`,
				warnings = warnings
			}
		end,
		
		validate = function(self: GripComponent, weapon: Weapon): ValidationResult
			-- Check if weapon has foregrip rail
			local hasForegripRail = false
			for _, attachmentPoint in pairs(weapon.attachmentPoints) do
				if attachmentPoint.name == "foregrip_rail" then
					hasForegripRail = true
					break
				end
			end
			
			if not hasForegripRail then
				return {
					isValid = false,
					errorCode = "ComponentIncompatible",
					severity = "Error",
					description = "Weapon does not have a foregrip rail",
					suggestedAction = "Reject"
				}
			end
			
			-- Check if grip type is appropriate for weapon category
			if weapon.category == "Pistol" and self.gripType == "Vertical" then
				return {
					isValid = false,
					errorCode = "ComponentIncompatible",
					severity = "Warning",
					description = "Vertical grips may not be suitable for pistols",
					suggestedAction = "Flag"
				}
			end
			
			return {
				isValid = true,
				severity = "Warning",
				description = "Grip is compatible with weapon",
				suggestedAction = "Allow"
			}
		end,
		
		onAttach = function(self: GripComponent, weapon: Weapon, attachmentPoint: AttachmentPoint): AttachResult
			attachmentPoint.occupied = true
			attachmentPoint.component = self
			
			-- Calculate modified stats
			local modifiedStats = self:modifyStatistics(weapon.baseStatistics)
			
			return {
				success = true,
				attachmentPoint = attachmentPoint.name,
				modifiedStats = modifiedStats,
				conflicts = {}
			}
		end,
		
		onDetach = function(self: GripComponent, weapon: Weapon): DetachResult
			for _, attachmentPoint in pairs(weapon.attachmentPoints) do
				if attachmentPoint.component == self then
					attachmentPoint.occupied = false
					attachmentPoint.component = nil
					break
				end
			end
			
			return {
				success = true,
				restoredStats = weapon.baseStatistics,
				dependentComponents = {}
			}
		end,
		
		-- ============================================================================
		-- RUNTIME INTERFACE METHODS
		-- ============================================================================
		
		onFire = function(self: GripComponent, weapon: Weapon, fireData: FireData): ()
			-- Grips help with recoil control during firing
			-- Minimal wear from use
		end,
		
		onReload = function(self: GripComponent, weapon: Weapon, reloadData: ReloadData): ()
			-- Grips improve reload handling
		end,
		
		onUpdate = function(self: GripComponent, weapon: Weapon, deltaTime: number): ()
			-- No continuous updates needed for grips
		end,
		
		-- ============================================================================
		-- GRIP-SPECIFIC METHODS
		-- ============================================================================
		
		modifyStatistics = function(self: GripComponent, baseStats: WeaponStatistics): WeaponStatistics
			local modifiedStats = table.clone(baseStats)
			
			-- Grip type affects recoil control
			if self.gripType == "Vertical" then
				-- Vertical grips provide excellent recoil control
				modifiedStats.recoil.vertical *= 0.85 -- 15% reduction
				modifiedStats.recoil.horizontal *= 0.90 -- 10% reduction
			elseif self.gripType == "Angled" then
				-- Angled grips provide moderate recoil control with better mobility
				modifiedStats.recoil.vertical *= 0.92 -- 8% reduction
				modifiedStats.recoil.horizontal *= 0.88 -- 12% reduction
				modifiedStats.recoil.recovery *= 1.1 -- 10% faster recovery
			elseif self.gripType == "Hand Stop" then
				-- Hand stops provide minimal recoil control but improve handling
				modifiedStats.recoil.vertical *= 0.95 -- 5% reduction
				modifiedStats.recoil.recovery *= 1.15 -- 15% faster recovery
			end
			
			-- Texture type affects control
			if self.textureType == "Aggressive" then
				-- Better grip but may be uncomfortable for extended use
				modifiedStats.recoil.horizontal *= 0.95
			elseif self.textureType == "Rubberized" then
				-- Comfortable grip with good control
				modifiedStats.recoil.vertical *= 0.97
				modifiedStats.recoil.horizontal *= 0.97
			end
			
			-- Ergonomic features
			if self.palmSwell then
				modifiedStats.recoil.recovery *= 1.05 -- Better control
			end
			
			if self.fingerGrooves then
				modifiedStats.accuracy *= 1.02 -- Slight accuracy improvement
			end
			
			if self.thumbRest then
				modifiedStats.recoil.horizontal *= 0.98 -- Better horizontal control
			end
			
			-- Grip tape provides additional control
			if self.gripTape then
				modifiedStats.recoil.vertical *= 0.98
				modifiedStats.recoil.horizontal *= 0.98
			end
			
			return modifiedStats
		end,
		
		modifyHandling = function(self: GripComponent, baseHandling: number): number
			local handlingBonus = 0.1 -- Base 10% improvement
			
			-- Grip type affects handling
			if self.gripType == "Vertical" then
				handlingBonus = 0.15 -- 15% improvement
			elseif self.gripType == "Angled" then
				handlingBonus = 0.12 -- 12% improvement
			elseif self.gripType == "Hand Stop" then
				handlingBonus = 0.08 -- 8% improvement
			end
			
			-- Ergonomic features improve handling
			if self.palmSwell then
				handlingBonus += 0.02
			end
			
			if self.fingerGrooves then
				handlingBonus += 0.02
			end
			
			if self.thumbRest then
				handlingBonus += 0.01
			end
			
			return baseHandling * (1 + handlingBonus)
		end,
		
		modifyControlSpeed = function(self: GripComponent, baseSpeed: number): number
			local speedBonus = 0.05 -- Base 5% improvement
			
			-- Angled grips and hand stops are better for quick movements
			if self.gripType == "Angled" then
				speedBonus = 0.08
			elseif self.gripType == "Hand Stop" then
				speedBonus = 0.10
			elseif self.gripType == "Vertical" then
				speedBonus = 0.03 -- Vertical grips are more stable but slower
			end
			
			-- Texture affects speed
			if self.textureType == "Smooth" then
				speedBonus *= 1.1 -- Faster but less control
			elseif self.textureType == "Aggressive" then
				speedBonus *= 0.9 -- Slower but more control
			end
			
			return baseSpeed * (1 + speedBonus)
		end,
		
		-- Maintenance and customization
		applyGripTape = function(self: GripComponent): boolean
			if self.gripTape then
				return false -- Already has grip tape
			end
			
			self.gripTape = true
			print(`[Grip] Applied grip tape to {self.id}`)
			return true
		end,
		
		removeGripTape = function(self: GripComponent): boolean
			if not self.gripTape then
				return false -- No grip tape to remove
			end
			
			self.gripTape = false
			print(`[Grip] Removed grip tape from {self.id}`)
			return true
		end,
		
		getErgonomicsReport = function(self: GripComponent): {[string]: any}
			return {
				gripType = self.gripType,
				gripAngle = self.gripAngle,
				textureType = self.textureType,
				palmSwell = self.palmSwell,
				fingerGrooves = self.fingerGrooves,
				thumbRest = self.thumbRest,
				gripTape = self.gripTape,
				wearPattern = self.wearPattern,
				
				-- Calculated benefits
				recoilReduction = self:_calculateRecoilReduction(),
				handlingImprovement = self:_calculateHandlingImprovement(),
				controlSpeedBonus = self:_calculateControlSpeedBonus(),
			}
		end,
		
		_calculateRecoilReduction = function(self: GripComponent): number
			local reduction = 0
			
			if self.gripType == "Vertical" then
				reduction = 0.15
			elseif self.gripType == "Angled" then
				reduction = 0.10
			elseif self.gripType == "Hand Stop" then
				reduction = 0.05
			end
			
			if self.gripTape then
				reduction += 0.02
			end
			
			return reduction
		end,
		
		_calculateHandlingImprovement = function(self: GripComponent): number
			local improvement = 0.1
			
			if self.gripType == "Vertical" then
				improvement = 0.15
			elseif self.gripType == "Angled" then
				improvement = 0.12
			end
			
			if self.palmSwell then improvement += 0.02 end
			if self.fingerGrooves then improvement += 0.02 end
			if self.thumbRest then improvement += 0.01 end
			
			return improvement
		end,
		
		_calculateControlSpeedBonus = function(self: GripComponent): number
			local bonus = 0.05
			
			if self.gripType == "Hand Stop" then
				bonus = 0.10
			elseif self.gripType == "Angled" then
				bonus = 0.08
			elseif self.gripType == "Vertical" then
				bonus = 0.03
			end
			
			return bonus
		end,
	}
	
	return grip
end

-- ============================================================================
-- FACTORY FUNCTION EXPORT
-- ============================================================================

return createGripComponent
