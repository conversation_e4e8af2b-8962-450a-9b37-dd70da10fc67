--!strict
--[[
	Gun System Framework - Performance Test Suite

	This module provides comprehensive performance testing for the GSF system:
	- Load testing with multiple weapons firing simultaneously
	- Memory usage monitoring and optimization
	- Frame rate analysis under stress
	- Object pooling efficiency testing
	- LOD system validation
	- Performance optimization recommendations
]]

local RunService = game:GetService("RunService")

local AdvancedComponentTest = require(script.Parent.AdvancedComponentTest)
local GSF = require(script.Parent.Parent.Core)

-- Type imports
local Types = require(script.Parent.Parent.Types)

local PerformanceTest = {}

-- Test configuration
local testConfig = {
  maxWeapons = 50,
  testDuration = 60, -- seconds
  fireRate = 10, -- shots per second per weapon
  enableEffects = true,
  enableAudio = true,
  enableLOD = true,
  targetFPS = 60,
}

-- Test state
local testState = {
  weapons = {},
  startTime = 0,
  shotsFired = 0,
  effectsCreated = 0,
  isRunning = false,
  performanceData = {},
}

-- ============================================================================
-- PERFORMANCE TEST SUITE
-- ============================================================================

function PerformanceTest.runComprehensivePerformanceTest(): ()
  print("\n⚡ === COMPREHENSIVE PERFORMANCE TEST ===")
  print("Testing GSF performance under load...")

  -- Test 1: Baseline performance
  print("\n--- Test 1: Baseline Performance ---")
  PerformanceTest.measureBaselinePerformance()

  -- Test 2: Object pooling efficiency
  print("\n--- Test 2: Object Pooling Efficiency ---")
  PerformanceTest.testObjectPooling()

  -- Test 3: LOD system effectiveness
  print("\n--- Test 3: LOD System Effectiveness ---")
  PerformanceTest.testLODSystem()

  -- Test 4: Memory management
  print("\n--- Test 4: Memory Management ---")
  PerformanceTest.testMemoryManagement()

  -- Test 5: Load testing
  print("\n--- Test 5: Load Testing ---")
  PerformanceTest.runLoadTest()

  -- Test 6: Performance optimization
  print("\n--- Test 6: Performance Optimization ---")
  PerformanceTest.testPerformanceOptimization()

  -- Generate final report
  print("\n--- Final Performance Report ---")
  PerformanceTest.generatePerformanceReport()

  print("\n✅ === PERFORMANCE TESTS COMPLETE ===")
end

function PerformanceTest.measureBaselinePerformance(): ()
  print("Measuring baseline performance...")

  -- Start performance monitoring
  GSF.PerformanceMonitor.startMonitoring()

  -- Wait for baseline measurement
  task.wait(5)

  local report = GSF.PerformanceMonitor.getPerformanceReport()

  print(`  Baseline FPS: {report.frameRate.average}`)
  print(`  Baseline Memory: {report.memory.current}MB`)
  print(`  Frame Rate Rating: {report.frameRate.rating}`)
  print(`  Memory Rating: {report.memory.rating}`)

  -- Store baseline for comparison
  testState.performanceData.baseline = report
end

function PerformanceTest.testObjectPooling(): ()
  print("Testing object pooling efficiency...")

  -- Test pool creation and allocation
  local testPoolName = "performance_test_pool"

  local success = GSF.ObjectPool.createPool(testPoolName, "Dynamic", function()
    local part = Instance.new("Part")
    part.Size = Vector3.new(1, 1, 1)
    part.Anchored = true
    return part
  end, { initialSize = 10, maxSize = 100 })

  if success then
    print("  ✅ Test pool created successfully")

    -- Test rapid allocation/deallocation
    local allocatedObjects = {}
    local startTime = tick()

    -- Allocate 50 objects
    for i = 1, 50 do
      local object = GSF.ObjectPool.allocate(testPoolName)
      if object then
        table.insert(allocatedObjects, object)
      end
    end

    local allocationTime = tick() - startTime
    print(`  Allocated 50 objects in {math.floor(allocationTime * 1000)}ms`)

    -- Deallocate all objects
    startTime = tick()
    for _, object in ipairs(allocatedObjects) do
      GSF.ObjectPool.deallocate(testPoolName, object)
    end

    local deallocationTime = tick() - startTime
    print(`  Deallocated 50 objects in {math.floor(deallocationTime * 1000)}ms`)

    -- Check pool statistics
    local poolStats = GSF.ObjectPool.getPoolStatistics(testPoolName)
    if poolStats then
      print(`  Pool hit rate: {math.floor(poolStats.hitRate * 100)}%`)
      print(`  Pool efficiency: {math.floor(poolStats.efficiency * 100)}%`)
    end

    -- Clean up test pool
    GSF.ObjectPool.destroyPool(testPoolName)
  else
    warn("  ❌ Failed to create test pool")
  end
end

function PerformanceTest.testLODSystem(): ()
  print("Testing LOD system effectiveness...")

  -- Test LOD level calculation at different distances
  local testPositions = {
    { distance = 50, expected = "Ultra/High" },
    { distance = 150, expected = "Medium" },
    { distance = 300, expected = "Low" },
    { distance = 600, expected = "Minimal" },
  }

  for _, test in ipairs(testPositions) do
    local position = { X = test.distance, Y = 0, Z = 0 }
    local lodLevel = GSF.LODSystem.calculateLODLevel(position, "effect")

    print(`  Distance {test.distance}m: LOD Level {lodLevel} ({test.expected})`)
  end

  -- Test adaptive LOD
  GSF.LODSystem.enableAdaptiveLOD(true)
  GSF.LODSystem.setPerformanceTarget(testConfig.targetFPS)

  print("  ✅ Adaptive LOD enabled")

  -- Get LOD statistics
  local lodStats = GSF.LODSystem.getLODStatistics()
  print(`  Global LOD Level: {lodStats.globalLODLevel}`)
  print(`  Adaptive LOD: {lodStats.adaptiveEnabled and "Enabled" or "Disabled"}`)
  print(`  Registered Objects: {lodStats.registeredObjects}`)
end

function PerformanceTest.testMemoryManagement(): ()
  print("Testing memory management...")

  local initialMemory = collectgarbage("count")
  print(`  Initial memory: {math.floor(initialMemory)}KB`)

  -- Create many temporary objects to test garbage collection
  local tempObjects = {}
  for i = 1, 1000 do
    local part = Instance.new("Part")
    part.Size = Vector3.new(math.random(), math.random(), math.random())
    table.insert(tempObjects, part)
  end

  local peakMemory = collectgarbage("count")
  print(`  Peak memory: {math.floor(peakMemory)}KB (+{math.floor(peakMemory - initialMemory)}KB)`)

  -- Clear objects and force garbage collection
  tempObjects = {}
  local freedMemory = GSF.PerformanceMonitor.forceGarbageCollection()

  local finalMemory = collectgarbage("count")
  print(`  Final memory: {math.floor(finalMemory)}KB`)
  print(`  Memory freed: {math.floor(freedMemory)}KB`)

  -- Test memory optimization
  GSF.PerformanceMonitor.optimizeMemoryUsage()
  print("  ✅ Memory optimization completed")
end

function PerformanceTest.runLoadTest(): ()
  print("Running load test with multiple weapons...")

  testState.isRunning = true
  testState.startTime = tick()
  testState.shotsFired = 0
  testState.effectsCreated = 0

  -- Create multiple weapons
  for i = 1, testConfig.maxWeapons do
    local weapon = AdvancedComponentTest.createTacticalRifle()
    AdvancedComponentTest.attachTacticalComponents(weapon)
    table.insert(testState.weapons, weapon)
  end

  print(`  Created {#testState.weapons} weapons`)

  -- Start firing simulation
  local fireConnection
  fireConnection = RunService.Heartbeat:Connect(function()
    if not testState.isRunning then
      fireConnection:Disconnect()
      return
    end

    -- Check test duration
    if tick() - testState.startTime >= testConfig.testDuration then
      testState.isRunning = false
      fireConnection:Disconnect()
      PerformanceTest._completeLoadTest()
      return
    end

    -- Fire weapons randomly
    for _, weapon in ipairs(testState.weapons) do
      if math.random() < (testConfig.fireRate / 60) then -- Convert to per-frame probability
        local fireResult = weapon:fire(
          { X = 0, Y = 5, Z = 0 },
          { X = math.random() - 0.5, Y = 0, Z = -1 }
        )

        if fireResult.success then
          testState.shotsFired += 1
          if testConfig.enableEffects then
            testState.effectsCreated += 2 -- Muzzle flash + shell ejection
          end
        end
      end
    end
  end)

  print(`  Load test started - firing for {testConfig.testDuration} seconds...`)
end

function PerformanceTest._completeLoadTest(): ()
  local duration = tick() - testState.startTime
  local avgFireRate = testState.shotsFired / duration

  print(`  Load test completed:`)
  print(`    Duration: {math.floor(duration)}s`)
  print(`    Shots fired: {testState.shotsFired}`)
  print(`    Average fire rate: {math.floor(avgFireRate)} shots/sec`)
  print(`    Effects created: {testState.effectsCreated}`)

  -- Get performance report
  local report = GSF.PerformanceMonitor.getPerformanceReport()
  print(`    Final FPS: {report.frameRate.average}`)
  print(`    Final Memory: {report.memory.current}MB`)

  -- Compare to baseline
  if testState.performanceData.baseline then
    local fpsChange = report.frameRate.average
      - testState.performanceData.baseline.frameRate.average
    local memoryChange = report.memory.current - testState.performanceData.baseline.memory.current

    print(`    FPS Change: {fpsChange > 0 and "+" or ""}{math.floor(fpsChange)}`)
    print(
      `    Memory Change: {memoryChange > 0 and "+" or ""}{math.floor(memoryChange * 100) / 100}MB`
    )
  end

  -- Clean up weapons
  testState.weapons = {}
end

function PerformanceTest.testPerformanceOptimization(): ()
  print("Testing performance optimization features...")

  -- Test automatic optimization
  local suggestions = GSF.PerformanceMonitor.getOptimizationSuggestions()

  print("  Optimization suggestions:")
  for _, suggestion in ipairs(suggestions) do
    print(`    • {suggestion}`)
  end

  -- Test LOD optimization
  GSF.LODSystem.setGlobalLODLevel(2) -- Medium quality
  print("  ✅ Set LOD to Medium for optimization")

  -- Test pool optimization
  GSF.ObjectPool.forceCleanupAll()
  print("  ✅ Forced cleanup of all object pools")

  -- Test memory optimization
  GSF.PerformanceMonitor.optimizeMemoryUsage()
  print("  ✅ Optimized memory usage")
end

function PerformanceTest.generatePerformanceReport(): ()
  print("Generating comprehensive performance report...")

  -- Performance monitor report
  GSF.PerformanceMonitor.printPerformanceReport()

  -- Object pool report
  GSF.ObjectPool.printPoolReport()

  -- LOD system report
  GSF.LODSystem.printLODReport()

  -- Overall assessment
  local report = GSF.PerformanceMonitor.getPerformanceReport()

  print("📊 OVERALL PERFORMANCE ASSESSMENT:")

  if report.frameRate.rating == "Excellent" and report.memory.rating == "Excellent" then
    print("  🟢 EXCELLENT - System performing optimally")
  elseif report.frameRate.rating == "Good" and report.memory.rating == "Good" then
    print("  🟡 GOOD - System performing well")
  elseif report.frameRate.rating == "Acceptable" or report.memory.rating == "Acceptable" then
    print("  🟠 ACCEPTABLE - Some optimization recommended")
  else
    print("  🔴 POOR - Optimization required")
  end

  -- Recommendations
  if #report.recommendations > 0 then
    print("\n💡 OPTIMIZATION RECOMMENDATIONS:")
    for _, recommendation in ipairs(report.recommendations) do
      print(`  • {recommendation}`)
    end
  end
end

-- ============================================================================
-- STRESS TESTING
-- ============================================================================

function PerformanceTest.runStressTest(): ()
  print("\n🔥 === STRESS TEST ===")
  print("Running extreme load test...")

  -- Increase test parameters for stress testing
  local stressConfig = {
    maxWeapons = 100,
    testDuration = 30,
    fireRate = 20,
    enableEffects = true,
    enableAudio = true,
  }

  -- Temporarily override config
  local originalConfig = testConfig
  testConfig = stressConfig

  -- Run stress test
  PerformanceTest.runLoadTest()

  -- Wait for completion
  while testState.isRunning do
    task.wait(1)
    local report = GSF.PerformanceMonitor.getPerformanceReport()
    print(
      `  Stress test progress - FPS: {math.floor(report.frameRate.current)}, Memory: {math.floor(
        report.memory.current
      )}MB`
    )
  end

  -- Restore original config
  testConfig = originalConfig

  print("🔥 Stress test completed!")
end

-- ============================================================================
-- QUICK PERFORMANCE TESTS
-- ============================================================================

function PerformanceTest.quickPerformanceCheck(): ()
  print("\n⚡ Quick Performance Check")

  -- Quick FPS measurement
  local fpsSum = 0
  local samples = 60 -- 1 second at 60 FPS

  for i = 1, samples do
    local frameStart = tick()
    RunService.Heartbeat:Wait()
    local frameTime = tick() - frameStart
    fpsSum += 1 / frameTime
  end

  local avgFPS = fpsSum / samples
  print(`  Average FPS: {math.floor(avgFPS)}`)

  -- Quick memory check
  local memory = collectgarbage("count") / 1024
  print(`  Memory Usage: {math.floor(memory * 100) / 100}MB`)

  -- Quick pool check
  local poolCount = GSF.ObjectPool.getActivePoolCount()
  print(`  Active Pools: {poolCount}`)

  -- Performance rating
  if avgFPS >= 60 and memory < 50 then
    print("  Rating: ✅ Excellent")
  elseif avgFPS >= 45 and memory < 100 then
    print("  Rating: ✅ Good")
  elseif avgFPS >= 30 and memory < 200 then
    print("  Rating: ⚠️ Acceptable")
  else
    print("  Rating: ❌ Poor")
  end
end

function PerformanceTest.benchmarkComponentOperations(): ()
  print("\n🔧 Component Operations Benchmark")

  -- Create test weapon
  local weapon = AdvancedComponentTest.createTacticalRifle()

  -- Benchmark component attachment
  local startTime = tick()
  for i = 1, 100 do
    local sight = GSF.createSight({
      id = `benchmark_sight_{i}`,
      magnification = 1.0,
      reticleType = "Dot",
    })
    weapon:attach(sight, "optic_rail")
    weapon:detach(sight)
  end
  local attachTime = tick() - startTime

  print(`  100 attach/detach operations: {math.floor(attachTime * 1000)}ms`)
  print(`  Average per operation: {math.floor(attachTime * 10)}ms`)

  -- Benchmark statistics calculation
  startTime = tick()
  for i = 1, 1000 do
    weapon:getEffectiveStatistics()
  end
  local statsTime = tick() - startTime

  print(`  1000 statistics calculations: {math.floor(statsTime * 1000)}ms`)
  print(`  Average per calculation: {math.floor(statsTime)}ms`)
end

return PerformanceTest
