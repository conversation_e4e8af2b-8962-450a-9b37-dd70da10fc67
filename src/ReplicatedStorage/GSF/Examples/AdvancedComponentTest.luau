--!strict
--[[
	Gun System Framework - Advanced Component Test

	This module demonstrates the advanced component system including
	sights, stocks, grips, and suppressors with realistic configurations.
]]

local GSF = require(script.Parent.Parent.Core)

-- Type imports
local Types = require(script.Parent.Parent.Types)

local AdvancedComponentTest = {}

-- ============================================================================
-- ADVANCED WEAPON CONFIGURATIONS
-- ============================================================================

function AdvancedComponentTest.createTacticalRifle(): Weapon
  print("[AdvancedComponentTest] Creating tactical rifle with advanced components...")

  -- Create base weapon statistics
  local weaponStats = {
    damage = 35,
    accuracy = 0.85,
    range = 600,
    fireRate = 650, -- RPM
    muzzleVelocity = 900, -- m/s
    recoil = {
      vertical = 0.8,
      horizontal = 0.4,
      recovery = 0.3,
    },
    reliability = 0.98,
    coolingRate = 0.04,
    reloadTime = {
      tactical = 2.3,
      empty = 3.1,
    },
    compatibleAmmo = { "556x45" },
    defaultAmmo = "556x45",
  }

  -- Create weapon configuration
  local weaponConfig = {
    id = `tactical_rifle_{tick()}`,
    name = "Tactical M4A1 Carbine",
    category = "AssaultRifle",
    version = "1.0.0",
    baseStatistics = weaponStats,
    initialReserve = 210,
    maxReserve = 420,
  }

  -- Create the weapon
  local weapon = GSF.WeaponEntity.new(weaponConfig)

  -- Add all attachment points
  weapon.attachmentPoints.optic_rail = {
    name = "optic_rail",
    position = { X = 0, Y = 0.1, Z = -0.2 },
    occupied = false,
    component = nil,
  }

  weapon.attachmentPoints.stock_mount = {
    name = "stock_mount",
    position = { X = 0, Y = 0, Z = 0.3 },
    occupied = false,
    component = nil,
  }

  weapon.attachmentPoints.foregrip_rail = {
    name = "foregrip_rail",
    position = { X = 0, Y = -0.1, Z = -0.1 },
    occupied = false,
    component = nil,
  }

  weapon.attachmentPoints.muzzle_device = {
    name = "muzzle_device",
    position = { X = 0, Y = 0, Z = -0.4 },
    occupied = false,
    component = nil,
  }

  print(`[AdvancedComponentTest] Created tactical rifle: {weapon.id}`)
  return weapon
end

function AdvancedComponentTest.createSniperRifle(): Weapon
  print("[AdvancedComponentTest] Creating sniper rifle with precision components...")

  -- Create sniper weapon statistics
  local weaponStats = {
    damage = 85,
    accuracy = 0.95,
    range = 1200,
    fireRate = 60, -- RPM (bolt action)
    muzzleVelocity = 850, -- m/s
    recoil = {
      vertical = 2.5,
      horizontal = 0.8,
      recovery = 1.2,
    },
    reliability = 0.99,
    coolingRate = 0.02,
    reloadTime = {
      tactical = 3.5,
      empty = 4.2,
    },
    compatibleAmmo = { "308win" },
    defaultAmmo = "308win",
  }

  -- Create weapon configuration
  local weaponConfig = {
    id = `sniper_rifle_{tick()}`,
    name = "Precision Sniper Rifle",
    category = "SniperRifle",
    version = "1.0.0",
    baseStatistics = weaponStats,
    initialReserve = 40,
    maxReserve = 80,
  }

  -- Create the weapon
  local weapon = GSF.WeaponEntity.new(weaponConfig)

  -- Add attachment points
  weapon.attachmentPoints.optic_rail = {
    name = "optic_rail",
    position = { X = 0, Y = 0.15, Z = -0.1 },
    occupied = false,
    component = nil,
  }

  weapon.attachmentPoints.stock_mount = {
    name = "stock_mount",
    position = { X = 0, Y = 0, Z = 0.4 },
    occupied = false,
    component = nil,
  }

  weapon.attachmentPoints.muzzle_device = {
    name = "muzzle_device",
    position = { X = 0, Y = 0, Z = -0.5 },
    occupied = false,
    component = nil,
  }

  print(`[AdvancedComponentTest] Created sniper rifle: {weapon.id}`)
  return weapon
end

-- ============================================================================
-- COMPONENT ATTACHMENT FUNCTIONS
-- ============================================================================

function AdvancedComponentTest.attachTacticalComponents(weapon: Weapon): ()
  print("[AdvancedComponentTest] Attaching tactical components...")

  -- Attach barrel
  local barrel = GSF.createBarrel({
    id = `barrel_{weapon.id}`,
    name = '14.5" Carbine Barrel',
    length = 14.5,
    bore = 5.56,
    rifling = "Standard",
    twist = 1.0,
    gasSystem = "DI",
  })

  if barrel then
    weapon:attach(barrel, "barrel_mount")
    print("  ✓ Attached carbine barrel")
  end

  -- Attach magazine
  local magazine = GSF.createMagazine({
    id = `magazine_{weapon.id}`,
    name = "30-Round PMAG",
    capacity = 30,
    compatibleAmmo = { "556x45" },
    feedReliability = 0.99,
  })

  if magazine then
    weapon:attach(magazine, "magazine_well")
    magazine:loadAmmunition("556x45", 30)
    weapon:reload(true)
    print("  ✓ Attached and loaded magazine")
  end

  -- Attach red dot sight
  local sight = GSF.createSight({
    id = `sight_{weapon.id}`,
    name = "EOTech Holographic Sight",
    magnification = 1.0,
    reticleType = "Holographic",
    batteryLevel = 0.9,
    illuminationLevel = 0.7,
  })

  if sight then
    weapon:attach(sight, "optic_rail")
    print("  ✓ Attached holographic sight")
  end

  -- Attach collapsible stock
  local stock = GSF.createStock({
    id = `stock_{weapon.id}`,
    name = "M4 Collapsible Stock",
    stockType = "Collapsible",
    lengthOfPull = 330,
    isAdjustable = true,
    minLength = 300,
    maxLength = 370,
    recoilReduction = 0.12,
  })

  if stock then
    weapon:attach(stock, "stock_mount")
    print("  ✓ Attached collapsible stock")
  end

  -- Attach vertical grip
  local grip = GSF.createGrip({
    id = `grip_{weapon.id}`,
    name = "Tactical Vertical Grip",
    gripType = "Vertical",
    textureType = "Aggressive",
    palmSwell = true,
    fingerGrooves = false,
  })

  if grip then
    weapon:attach(grip, "foregrip_rail")
    print("  ✓ Attached vertical grip")
  end

  -- Attach suppressor
  local suppressor = GSF.createSuppressor({
    id = `suppressor_{weapon.id}`,
    name = "SureFire SOCOM Suppressor",
    suppressorType = "Baffle",
    dbReduction = 35,
    boreSize = 6.0,
    baffleCount = 10,
    velocityChange = -10,
    backPressure = 1.15,
  })

  if suppressor then
    weapon:attach(suppressor, "muzzle_device")
    print("  ✓ Attached suppressor")
  end

  print("[AdvancedComponentTest] Tactical configuration complete!")
end

function AdvancedComponentTest.attachSniperComponents(weapon: Weapon): ()
  print("[AdvancedComponentTest] Attaching sniper components...")

  -- Attach precision barrel
  local barrel = GSF.createBarrel({
    id = `barrel_{weapon.id}`,
    name = '24" Heavy Barrel',
    length = 24,
    bore = 7.62,
    rifling = "Match",
    twist = 1.2,
    gasSystem = "None", -- Bolt action
  })

  if barrel then
    weapon:attach(barrel, "barrel_mount")
    print("  ✓ Attached heavy barrel")
  end

  -- Attach precision magazine
  local magazine = GSF.createMagazine({
    id = `magazine_{weapon.id}`,
    name = "10-Round Precision Magazine",
    capacity = 10,
    compatibleAmmo = { "308win" },
    feedReliability = 0.995,
  })

  if magazine then
    weapon:attach(magazine, "magazine_well")
    magazine:loadAmmunition("308win", 10)
    weapon:reload(true)
    print("  ✓ Attached and loaded precision magazine")
  end

  -- Attach high-power scope
  local sight = GSF.createSight({
    id = `sight_{weapon.id}`,
    name = "Leupold Mark 5HD 3.6-18x",
    magnification = 10.0, -- Variable, set to mid-range
    reticleType = "Mil-Dot",
    fieldOfView = 3.6,
    zeroRange = 300,
    elevationRange = 30,
    windageRange = 15,
  })

  if sight then
    weapon:attach(sight, "optic_rail")
    print("  ✓ Attached precision scope")
  end

  -- Attach precision stock
  local stock = GSF.createStock({
    id = `stock_{weapon.id}`,
    name = "McMillan A5 Precision Stock",
    stockType = "Fixed",
    lengthOfPull = 365,
    recoilPadType = "Gel",
    recoilReduction = 0.25,
    combRise = 5,
  })

  if stock then
    weapon:attach(stock, "stock_mount")
    print("  ✓ Attached precision stock")
  end

  -- Attach precision suppressor
  local suppressor = GSF.createSuppressor({
    id = `suppressor_{weapon.id}`,
    name = "Thunder Beast Ultra 9",
    suppressorType = "Monocore",
    dbReduction = 38,
    boreSize = 8.0,
    baffleCount = 12,
    velocityChange = -5, -- Minimal velocity loss
    backPressure = 0.95, -- Reduced back pressure
  })

  if suppressor then
    weapon:attach(suppressor, "muzzle_device")
    print("  ✓ Attached precision suppressor")
  end

  print("[AdvancedComponentTest] Sniper configuration complete!")
end

-- ============================================================================
-- TEST SCENARIOS
-- ============================================================================

function AdvancedComponentTest.runComponentTests(): ()
  print("\n=== Advanced Component System Test ===")

  -- Test 1: Tactical rifle configuration
  print("\n--- Test 1: Tactical Rifle Configuration ---")
  local tacticalRifle = AdvancedComponentTest.createTacticalRifle()
  AdvancedComponentTest.attachTacticalComponents(tacticalRifle)

  -- Analyze tactical rifle performance
  local tacticalStats = tacticalRifle:getEffectiveStatistics()
  print("\nTactical Rifle Performance:")
  print(`  Accuracy: {math.floor(tacticalStats.accuracy * 100)}%`)
  print(`  Recoil Reduction: {math.floor((1 - tacticalStats.recoil.vertical) * 100)}%`)
  print(`  Effective Range: {tacticalStats.range}m`)

  -- Test firing with tactical rifle
  print("\nTesting tactical rifle firing...")
  for i = 1, 5 do
    local fireResult = tacticalRifle:fire()
    if fireResult.success then
      print(`  Shot {i}: Hit probability {math.floor(fireResult.accuracy * 100)}%`)
    end
  end

  -- Test 2: Sniper rifle configuration
  print("\n--- Test 2: Sniper Rifle Configuration ---")
  local sniperRifle = AdvancedComponentTest.createSniperRifle()
  AdvancedComponentTest.attachSniperComponents(sniperRifle)

  -- Analyze sniper rifle performance
  local sniperStats = sniperRifle:getEffectiveStatistics()
  print("\nSniper Rifle Performance:")
  print(`  Accuracy: {math.floor(sniperStats.accuracy * 100)}%`)
  print(`  Recoil Reduction: {math.floor((1 - sniperStats.recoil.vertical) * 100)}%`)
  print(`  Effective Range: {sniperStats.range}m`)

  -- Test precision shooting
  print("\nTesting precision shooting...")
  for i = 1, 3 do
    local fireResult = sniperRifle:fire()
    if fireResult.success then
      print(`  Precision shot {i}: Hit probability {math.floor(fireResult.accuracy * 100)}%`)
    end
  end

  -- Test 3: Component swapping
  print("\n--- Test 3: Component Swapping ---")
  AdvancedComponentTest.testComponentSwapping(tacticalRifle)

  -- Test 4: Component degradation
  print("\n--- Test 4: Component Degradation ---")
  AdvancedComponentTest.testComponentDegradation(tacticalRifle)

  print("\n=== Advanced Component Tests Complete ===")
end

function AdvancedComponentTest.testComponentSwapping(weapon: Weapon): ()
  print("Testing component swapping...")

  -- Get current sight
  local currentSight = weapon:getComponent("Sight")
  if currentSight then
    print(`  Current sight: {currentSight.name}`)

    -- Detach current sight
    local detachResult = weapon:detach(currentSight)
    if detachResult.success then
      print("  ✓ Detached current sight")

      -- Attach ACOG scope
      local acogScope = GSF.createSight({
        id = `acog_{weapon.id}`,
        name = "ACOG 4x Scope",
        magnification = 4.0,
        reticleType = "ACOG",
        fieldOfView = 15,
        zeroRange = 200,
      })

      if acogScope then
        local attachResult = weapon:attach(acogScope, "optic_rail")
        if attachResult.success then
          print("  ✓ Attached ACOG scope")

          -- Compare performance
          local newStats = weapon:getEffectiveStatistics()
          print(`  New accuracy: {math.floor(newStats.accuracy * 100)}%`)
        end
      end
    end
  end
end

function AdvancedComponentTest.testComponentDegradation(weapon: Weapon): ()
  print("Testing component degradation...")

  -- Get suppressor for degradation test
  local suppressor = weapon:getComponent("Suppressor")
  if suppressor then
    print(`  Testing suppressor: {suppressor.name}`)

    -- Simulate extended use
    for i = 1, 100 do
      suppressor:onFire(weapon, {
        roundType = "556x45",
        velocity = 900,
        accuracy = 0.85,
      })
    end

    -- Check condition
    local condition = suppressor:getConditionReport()
    print(`  Carbon buildup: {condition.carbonBuildup}%`)
    print(`  Baffle wear: {condition.baffleWear}%`)
    print(`  Current temperature: {condition.currentTemperature}°C`)
    print(`  Effective suppression: {math.floor(condition.effectiveSuppression)}dB`)

    if condition.maintenanceRequired then
      print("  ⚠️ Maintenance required!")
    end
  end
end

-- ============================================================================
-- PERFORMANCE ANALYSIS
-- ============================================================================

function AdvancedComponentTest.analyzeComponentPerformance(weapon: Weapon): { [string]: any }
  local baseStats = weapon.baseStatistics
  local effectiveStats = weapon:getEffectiveStatistics()

  local analysis = {
    weaponId = weapon.id,
    weaponName = weapon.name,
    attachedComponents = {},
    performanceGains = {},
    recommendations = {},
  }

  -- Analyze attached components
  for componentType, component in pairs(weapon.attachedComponents) do
    table.insert(analysis.attachedComponents, {
      type = componentType,
      name = component.name,
      id = component.id,
    })
  end

  -- Calculate performance gains
  analysis.performanceGains = {
    accuracyGain = (effectiveStats.accuracy - baseStats.accuracy) / baseStats.accuracy,
    recoilReduction = (baseStats.recoil.vertical - effectiveStats.recoil.vertical)
      / baseStats.recoil.vertical,
    rangeIncrease = (effectiveStats.range - baseStats.range) / baseStats.range,
    reliabilityChange = (effectiveStats.reliability - baseStats.reliability)
      / baseStats.reliability,
  }

  -- Generate recommendations
  if analysis.performanceGains.accuracyGain < 0.1 then
    table.insert(analysis.recommendations, "Consider upgrading sight for better accuracy")
  end

  if analysis.performanceGains.recoilReduction < 0.15 then
    table.insert(
      analysis.recommendations,
      "Consider adding or upgrading stock/grip for recoil control"
    )
  end

  return analysis
end

return AdvancedComponentTest
