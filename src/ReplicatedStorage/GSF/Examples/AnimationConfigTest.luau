--!strict
--[[
	Gun System Framework - Animation & Configuration Test
	
	This module demonstrates the new CFrame-based animation system and
	centralized configuration system:
	- CFrame-based procedural animations
	- Recoil, sway, reload, and inspection animations
	- Configuration system usage
	- Feature enable/disable functionality
	- Animation customization
]]

local GSF = require(script.Parent.Parent.Core)

local AnimationConfigTest = {}

-- ============================================================================
-- ANIMATION SYSTEM TESTS
-- ============================================================================

function AnimationConfigTest.runAnimationSystemTest(): ()
	print("\n🎬 === ANIMATION SYSTEM TEST ===")
	print("Testing CFrame-based animation system...")
	
	-- Test 1: Animation system availability
	print("\n--- Test 1: Animation System Availability ---")
	AnimationConfigTest.testAnimationSystemAvailability()
	
	-- Test 2: Create weapon with custom animations
	print("\n--- Test 2: Weapon with Custom Animations ---")
	local weapon = AnimationConfigTest.createAnimatedWeapon()
	
	-- Test 3: Test recoil animations
	print("\n--- Test 3: Recoil Animation Test ---")
	AnimationConfigTest.testRecoilAnimations(weapon)
	
	-- Test 4: Test reload animations
	print("\n--- Test 4: Reload Animation Test ---")
	AnimationConfigTest.testReloadAnimations(weapon)
	
	-- Test 5: Test inspection animations
	print("\n--- Test 5: Inspection Animation Test ---")
	AnimationConfigTest.testInspectionAnimations(weapon)
	
	print("\n✅ Animation system tests complete!")
end

function AnimationConfigTest.testAnimationSystemAvailability(): ()
	if GSF.AnimationSystem then
		print("  ✅ Animation System available")
		
		-- Test default settings
		local defaultSettings = GSF.AnimationSystem.getDefaultSettings()
		if defaultSettings and defaultSettings.recoil then
			print("  ✅ Default animation settings loaded")
			print(`    Recoil intensity: {defaultSettings.recoil.verticalIntensity}`)
			print(`    Sway intensity: {defaultSettings.sway.intensity}`)
		else
			warn("  ❌ Default animation settings missing")
		end
	else
		warn("  ❌ Animation System not available")
	end
end

function AnimationConfigTest.createAnimatedWeapon(): any
	print("Creating weapon with custom animation settings...")
	
	local weapon = GSF.WeaponEntity.new({
		id = "animated_test_rifle",
		name = "Animated Test Rifle",
		category = "AssaultRifle",
		
		baseStatistics = {
			damage = 35,
			accuracy = 0.8,
			range = 500,
			fireRate = 650,
			muzzleVelocity = 850,
			recoil = {
				vertical = 0.15,
				horizontal = 0.08,
			},
		},
		
		-- Custom animation settings
		animationSettings = {
			recoil = {
				enabled = true,
				verticalIntensity = 0.12,
				horizontalIntensity = 0.06,
				duration = 0.18,
				recoveryTime = 0.35,
				randomness = 0.25,
			},
			
			sway = {
				enabled = true,
				intensity = 0.025,
				frequency = 1.2,
				breathingEffect = true,
			},
			
			reload = {
				enabled = true,
				magazineDropTime = 0.9,
				magazineInsertTime = 1.6,
				boltReleaseTime = 2.3,
				smoothing = 0.12,
			},
			
			inspection = {
				enabled = true,
				rotationAmount = 50,
				duration = 2.5,
				smoothing = 0.18,
			},
		},
		
		attachmentPoints = {
			barrel_mount = {
				name = "barrel_mount",
				position = {X = 0, Y = 0, Z = 0.5},
				occupied = false,
				component = nil,
			},
			magazine_well = {
				name = "magazine_well",
				position = {X = 0, Y = -0.3, Z = 0.1},
				occupied = false,
				component = nil,
			},
		},
	})
	
	print("  ✅ Animated weapon created")
	print(`    Animation settings: {weapon.animationSettings and "Custom" or "Default"}`)
	
	return weapon
end

function AnimationConfigTest.testRecoilAnimations(weapon: any): ()
	print("Testing recoil animations...")
	
	-- Create a mock weapon model for animation testing
	local weaponModel = Instance.new("Model")
	weaponModel.Name = "TestWeaponModel"
	weaponModel.Parent = workspace
	
	-- Create a primary part
	local primaryPart = Instance.new("Part")
	primaryPart.Name = "Primary"
	primaryPart.Size = Vector3.new(4, 0.5, 0.2)
	primaryPart.Anchored = true
	primaryPart.Parent = weaponModel
	weaponModel.PrimaryPart = primaryPart
	
	-- Create weapon animator
	if GSF.AnimationSystem then
		weapon.animator = GSF.AnimationSystem.createWeaponAnimator(weapon, weaponModel)
		
		if weapon.animator then
			print("  ✅ Weapon animator created")
			
			-- Test recoil animation
			weapon.animator:playRecoilAnimation({
				recoilIntensity = 1.0,
				weaponType = weapon.category,
			})
			
			print("  ✅ Recoil animation triggered")
			print("    (Animation playing procedurally with CFrame)")
			
			-- Wait a moment to see animation
			task.wait(0.5)
			
			-- Test multiple rapid fires
			for i = 1, 3 do
				weapon.animator:playRecoilAnimation({
					recoilIntensity = 0.8 + (i * 0.1),
					weaponType = weapon.category,
				})
				task.wait(0.2)
			end
			
			print("  ✅ Rapid fire recoil test complete")
		else
			warn("  ❌ Failed to create weapon animator")
		end
	else
		warn("  ❌ Animation system not available")
	end
	
	-- Cleanup
	task.wait(2)
	weaponModel:Destroy()
end

function AnimationConfigTest.testReloadAnimations(weapon: any): ()
	print("Testing reload animations...")
	
	if weapon.animator then
		-- Test empty reload
		print("  Testing empty reload animation...")
		weapon.animator:playReloadAnimation("empty")
		
		task.wait(1)
		
		-- Test tactical reload
		print("  Testing tactical reload animation...")
		weapon.animator:playReloadAnimation("tactical")
		
		print("  ✅ Reload animations triggered")
		print("    (Animations use CFrame-based sequences)")
	else
		warn("  ❌ No animator available for reload test")
	end
end

function AnimationConfigTest.testInspectionAnimations(weapon: any): ()
	print("Testing inspection animations...")
	
	if weapon.animator then
		-- Test inspection
		print("  Testing inspection animation...")
		weapon.animator:playInspectionAnimation()
		
		print("  ✅ Inspection animation triggered")
		print("    (Animation rotates weapon using CFrame)")
		
		-- Wait for animation to complete
		task.wait(3)
	else
		warn("  ❌ No animator available for inspection test")
	end
end

-- ============================================================================
-- CONFIGURATION SYSTEM TESTS
-- ============================================================================

function AnimationConfigTest.runConfigurationSystemTest(): ()
	print("\n⚙️ === CONFIGURATION SYSTEM TEST ===")
	print("Testing centralized configuration system...")
	
	-- Test 1: Configuration system availability
	print("\n--- Test 1: Configuration System Availability ---")
	AnimationConfigTest.testConfigurationAvailability()
	
	-- Test 2: Feature enable/disable
	print("\n--- Test 2: Feature Enable/Disable ---")
	AnimationConfigTest.testFeatureToggling()
	
	-- Test 3: Configuration profiles
	print("\n--- Test 3: Configuration Profiles ---")
	AnimationConfigTest.testConfigurationProfiles()
	
	-- Test 4: Bulk configuration
	print("\n--- Test 4: Bulk Configuration ---")
	AnimationConfigTest.testBulkConfiguration()
	
	-- Test 5: Animation configuration
	print("\n--- Test 5: Animation Configuration ---")
	AnimationConfigTest.testAnimationConfiguration()
	
	print("\n✅ Configuration system tests complete!")
end

function AnimationConfigTest.testConfigurationAvailability(): ()
	if GSF.ConfigurationSystem then
		print("  ✅ Configuration System available")
		
		-- Test getting current configuration
		local currentConfig = GSF.ConfigurationSystem.getConfiguration()
		if currentConfig then
			print("  ✅ Current configuration accessible")
			
			-- Show some key settings
			local weaponConfig = GSF.ConfigurationSystem.getConfiguration("weapon")
			if weaponConfig then
				print(`    Jamming enabled: {weaponConfig.enableJamming and "YES" or "NO"}`)
				print(`    Overheating enabled: {weaponConfig.enableOverheating and "YES" or "NO"}`)
				print(`    Recoil enabled: {weaponConfig.enableRecoil and "YES" or "NO"}`)
			end
		else
			warn("  ❌ Configuration not accessible")
		end
	else
		warn("  ❌ Configuration System not available")
	end
end

function AnimationConfigTest.testFeatureToggling(): ()
	print("Testing feature enable/disable...")
	
	if not GSF.ConfigurationSystem then
		warn("  ❌ Configuration system not available")
		return
	end
	
	-- Test disabling jamming (as requested)
	GSF.ConfigurationSystem.disableFeature("weapon", "enableJamming")
	local jammingEnabled = GSF.ConfigurationSystem.isFeatureEnabled("weapon", "enableJamming")
	print(`  Jamming disabled: {not jammingEnabled and "✅ SUCCESS" or "❌ FAILED"}`)
	
	-- Test disabling overheating (as requested)
	GSF.ConfigurationSystem.disableFeature("weapon", "enableOverheating")
	local overheatingEnabled = GSF.ConfigurationSystem.isFeatureEnabled("weapon", "enableOverheating")
	print(`  Overheating disabled: {not overheatingEnabled and "✅ SUCCESS" or "❌ FAILED"}`)
	
	-- Test enabling CFrame animations
	GSF.ConfigurationSystem.enableFeature("animations", "enableCFrameAnimations")
	local cframeEnabled = GSF.ConfigurationSystem.isFeatureEnabled("animations", "enableCFrameAnimations")
	print(`  CFrame animations enabled: {cframeEnabled and "✅ SUCCESS" or "❌ FAILED"}`)
	
	-- Test toggle functionality
	GSF.ConfigurationSystem.toggleFeature("effects", "enableMuzzleFlash")
	print("  ✅ Feature toggle test complete")
end

function AnimationConfigTest.testConfigurationProfiles(): ()
	print("Testing configuration profiles...")
	
	if not GSF.ConfigurationSystem then
		warn("  ❌ Configuration system not available")
		return
	end
	
	-- Test available profiles
	local profiles = GSF.ConfigurationSystem.getAvailableProfiles()
	print(`  Available profiles: {table.concat(profiles, ", ")}`)
	
	-- Test loading performance profile
	GSF.ConfigurationSystem.loadProfile("Performance")
	print("  ✅ Performance profile loaded")
	
	-- Test loading high-end profile
	GSF.ConfigurationSystem.loadProfile("HighEnd")
	print("  ✅ High-end profile loaded")
	
	-- Test creating custom profile
	GSF.ConfigurationSystem.createProfile("TestProfile", {
		weapon = {
			enableJamming = false,
			enableOverheating = false,
			enableRecoil = true,
		},
		animations = {
			enableCFrameAnimations = true,
			recoilIntensity = 1.5,
		},
	})
	
	GSF.ConfigurationSystem.loadProfile("TestProfile")
	print("  ✅ Custom profile created and loaded")
end

function AnimationConfigTest.testBulkConfiguration(): ()
	print("Testing bulk configuration...")
	
	if not GSF.ConfigurationSystem then
		warn("  ❌ Configuration system not available")
		return
	end
	
	-- Test bulk configuration
	GSF.ConfigurationSystem.setBulkConfiguration({
		weapon = {
			enableJamming = false,
			enableOverheating = false,
			enableRecoil = true,
			enableSpread = true,
		},
		animations = {
			enableCFrameAnimations = true,
			enableRecoilAnimation = true,
			enableSwayAnimation = true,
			recoilIntensity = 1.2,
			swayIntensity = 0.8,
		},
		effects = {
			enableMuzzleFlash = true,
			enableShellEjection = true,
			effectQuality = "High",
		},
	})
	
	print("  ✅ Bulk configuration applied")
	
	-- Verify settings
	local jammingDisabled = not GSF.ConfigurationSystem.isFeatureEnabled("weapon", "enableJamming")
	local overheatingDisabled = not GSF.ConfigurationSystem.isFeatureEnabled("weapon", "enableOverheating")
	local animationsEnabled = GSF.ConfigurationSystem.isFeatureEnabled("animations", "enableCFrameAnimations")
	
	print(`    Jamming disabled: {jammingDisabled and "✅" or "❌"}`)
	print(`    Overheating disabled: {overheatingDisabled and "✅" or "❌"}`)
	print(`    CFrame animations: {animationsEnabled and "✅" or "❌"}`)
end

function AnimationConfigTest.testAnimationConfiguration(): ()
	print("Testing animation-specific configuration...")
	
	if not GSF.ConfigurationSystem then
		warn("  ❌ Configuration system not available")
		return
	end
	
	-- Test animation intensity settings
	GSF.ConfigurationSystem.setConfiguration("animations", "recoilIntensity", 1.5)
	GSF.ConfigurationSystem.setConfiguration("animations", "swayIntensity", 0.7)
	GSF.ConfigurationSystem.setConfiguration("animations", "animationSmoothness", 0.08)
	
	print("  ✅ Animation intensity configured")
	
	-- Test enabling/disabling specific animations
	GSF.ConfigurationSystem.enableFeature("animations", "enableRecoilAnimation")
	GSF.ConfigurationSystem.enableFeature("animations", "enableSwayAnimation")
	GSF.ConfigurationSystem.enableFeature("animations", "enableReloadAnimation")
	GSF.ConfigurationSystem.enableFeature("animations", "enableInspectionAnimation")
	
	print("  ✅ All animations enabled")
	
	-- Show current animation configuration
	local animConfig = GSF.ConfigurationSystem.getConfiguration("animations")
	if animConfig then
		print("  Current animation settings:")
		print(`    Recoil intensity: {animConfig.recoilIntensity}`)
		print(`    Sway intensity: {animConfig.swayIntensity}`)
		print(`    CFrame animations: {animConfig.enableCFrameAnimations and "ON" or "OFF"}`)
	end
end

-- ============================================================================
-- COMPREHENSIVE TEST
-- ============================================================================

function AnimationConfigTest.runComprehensiveTest(): ()
	print("\n🎯 === COMPREHENSIVE ANIMATION & CONFIG TEST ===")
	
	-- Run configuration tests first
	AnimationConfigTest.runConfigurationSystemTest()
	
	-- Then run animation tests
	AnimationConfigTest.runAnimationSystemTest()
	
	-- Final configuration summary
	print("\n--- Final Configuration Summary ---")
	if GSF.ConfigurationSystem then
		GSF.ConfigurationSystem.printConfiguration()
	end
	
	print("\n🎉 === ALL TESTS COMPLETE ===")
	print("✅ CFrame-based animations working")
	print("✅ Configuration system functional")
	print("✅ Jamming and overheating disabled")
	print("✅ Customizable animation settings")
end

return AnimationConfigTest
