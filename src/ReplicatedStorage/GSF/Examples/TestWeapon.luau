--!strict
--[[
	Gun System Framework - Test Weapon Example

	This module creates a simple test weapon to validate the core architecture
	and demonstrate the firing mechanics. It shows how to:

	- Create a weapon using the WeaponEntity system
	- Attach components (barrel and magazine)
	- Configure weapon statistics
	- Demonstrate firing and reloading
	- Handle events and effects
]]

local GSF = require(script.Parent.Parent.Core)
local Types = require(script.Parent.Parent.Types)

-- Type imports
type Weapon = Types.Weapon
type WeaponStatistics = Types.WeaponStatistics
type DamageProfile = Types.DamageProfile
type Vector3 = Types.Vector3
type ProjectileConfig = Types.ProjectileConfig
type BallisticProperties = Types.BallisticProperties

-- ============================================================================
-- TEST WEAPON FACTORY
-- ============================================================================

local TestWeapon = {}

function TestWeapon.createBasicAssaultRifle(): Weapon
  -- Define weapon statistics
  local weaponStats: WeaponStatistics = {
    damage = {
      baseDamage = 30,
      headMultiplier = 2.0,
      chestMultiplier = 1.0,
      limbMultiplier = 0.8,
      falloffStart = 50,
      falloffEnd = 200,
      minimumDamage = 18,
    },
    accuracy = 0.80,
    range = 300,
    fireRate = 600, -- 600 RPM
    muzzleVelocity = 850, -- m/s
    recoil = {
      vertical = 0.4,
      horizontal = 0.15,
      pattern = {
        { X = 0, Y = 0.4, Z = 0 },
        { X = 0.1, Y = 0.5, Z = 0 },
        { X = -0.1, Y = 0.6, Z = 0 },
        { X = 0.15, Y = 0.7, Z = 0 },
        { X = -0.05, Y = 0.8, Z = 0 },
      },
      recovery = 0.7,
    },
    weight = 3.2, -- kg
    length = 76, -- cm
    reliability = 0.96,
    durabilityLoss = 0.0001,
    heatGeneration = 0.08,
    coolingRate = 0.04,
    reloadTime = {
      tactical = 2.3,
      empty = 3.1,
    },
    compatibleAmmo = { "556x45" },
    defaultAmmo = "556x45",
  }

  -- Create weapon configuration
  local weaponConfig = {
    id = `test_ar_{tick()}`,
    name = "Test M4A1 Assault Rifle",
    category = "AssaultRifle",
    version = "1.0.0",
    baseStatistics = weaponStats,
    initialReserve = 180,
    maxReserve = 300,
  }

  -- Create the weapon
  local weapon = GSF.WeaponEntity.new(weaponConfig)

  -- Add additional attachment points for new components
  weapon.attachmentPoints.optic_rail = {
    name = "optic_rail",
    position = { X = 0, Y = 0.1, Z = -0.2 },
    occupied = false,
    component = nil,
  }

  weapon.attachmentPoints.stock_mount = {
    name = "stock_mount",
    position = { X = 0, Y = 0, Z = 0.3 },
    occupied = false,
    component = nil,
  }

  weapon.attachmentPoints.foregrip_rail = {
    name = "foregrip_rail",
    position = { X = 0, Y = -0.1, Z = -0.1 },
    occupied = false,
    component = nil,
  }

  weapon.attachmentPoints.muzzle_device = {
    name = "muzzle_device",
    position = { X = 0, Y = 0, Z = -0.4 },
    occupied = false,
    component = nil,
  }

  print(`[TestWeapon] Created test assault rifle: {weapon.id}`)
  return weapon
end

function TestWeapon.createTestComponents(weapon: Weapon): ()
  -- Create a standard barrel
  local barrelConfig = {
    id = `barrel_{weapon.id}`,
    name = '16" Standard Barrel',
    description = "Standard 16-inch barrel for assault rifles",
    length = 16,
    mass = 1.2,
    accuracyModifier = 0.05, -- +5% accuracy
    customProperties = {
      bore = 5.56,
      rifling = "Standard",
      twist = 1.0,
      gasSystem = "DI",
    },
  }

  local barrel = GSF.ComponentRegistry.createComponent("Barrel", barrelConfig)
  if barrel then
    local attachResult = weapon:attach(barrel, "barrel_mount")
    if attachResult.success then
      print(`[TestWeapon] Attached barrel to {weapon.id}`)
    else
      warn(`[TestWeapon] Failed to attach barrel: {attachResult}`)
    end
  end

  -- Create a standard magazine
  local magazineConfig = {
    id = `magazine_{weapon.id}`,
    name = "30-Round Magazine",
    description = "Standard 30-round magazine",
    mass = 0.4,
    customProperties = {
      capacity = 30,
      compatibleAmmo = { "556x45" },
      feedReliability = 0.98,
    },
  }

  local magazine = GSF.ComponentRegistry.createComponent("Magazine", magazineConfig)
  if magazine then
    local attachResult = weapon:attach(magazine, "magazine_well")
    if attachResult.success then
      print(`[TestWeapon] Attached magazine to {weapon.id}`)

      -- Load the magazine with ammunition
      local loadResult = magazine:loadAmmunition("556x45", 30)
      if loadResult.success then
        print(`[TestWeapon] Loaded {loadResult.roundsLoaded} rounds into magazine`)

        -- Perform initial reload to chamber a round
        local reloadResult = weapon:reload(true) -- Force empty reload
        if reloadResult.success then
          print(`[TestWeapon] Initial reload completed: {reloadResult.reloadType}`)
        end
      end
    else
      warn(`[TestWeapon] Failed to attach magazine: {attachResult}`)
    end
  end

  -- Create a red dot sight
  local sight = GSF.createSight({
    id = `sight_{weapon.id}`,
    name = "Red Dot Sight",
    magnification = 1.0,
    reticleType = "Dot",
    batteryLevel = 1.0,
  })

  if sight then
    local attachResult = weapon:attach(sight, "optic_rail")
    if attachResult.success then
      print(`[TestWeapon] Attached sight to {weapon.id}`)
    else
      warn(`[TestWeapon] Failed to attach sight`)
    end
  end

  -- Create a stock
  local stock = GSF.createStock({
    id = `stock_{weapon.id}`,
    name = "Fixed Stock",
    stockType = "Fixed",
    lengthOfPull = 350,
    recoilReduction = 0.15,
  })

  if stock then
    local attachResult = weapon:attach(stock, "stock_mount")
    if attachResult.success then
      print(`[TestWeapon] Attached stock to {weapon.id}`)
    else
      warn(`[TestWeapon] Failed to attach stock`)
    end
  end

  -- Create a vertical grip
  local grip = GSF.createGrip({
    id = `grip_{weapon.id}`,
    name = "Vertical Grip",
    gripType = "Vertical",
    textureType = "Stippled",
  })

  if grip then
    local attachResult = weapon:attach(grip, "foregrip_rail")
    if attachResult.success then
      print(`[TestWeapon] Attached grip to {weapon.id}`)
    else
      warn(`[TestWeapon] Failed to attach grip`)
    end
  end
end

-- ============================================================================
-- TEST SCENARIOS
-- ============================================================================

function TestWeapon.demonstrateFiring(weapon: Weapon): ()
  print(`[TestWeapon] Demonstrating firing with {weapon.name}`)

  -- Check if weapon can fire
  if not weapon:canFire() then
    warn("[TestWeapon] Weapon cannot fire!")
    return
  end

  -- Set up firing parameters
  local origin = { X = 0, Y = 10, Z = 0 }
  local direction = { X = 0, Y = 0, Z = 1 } -- Forward

  -- Fire several rounds
  for i = 1, 5 do
    local fireResult = weapon:fire(origin, direction)

    if fireResult.success then
      print(`[TestWeapon] Shot {i}: Projectile {fireResult.projectileId} fired`)
      print(`  - Origin: ({fireResult.origin.X}, {fireResult.origin.Y}, {fireResult.origin.Z})`)
      print(`  - Velocity: {fireResult.velocity} m/s`)
      print(
        `  - Recoil: ({fireResult.recoilVector.X}, {fireResult.recoilVector.Y}, {fireResult.recoilVector.Z})`
      )

      -- Create projectile for physics simulation
      local projectileConfig: ProjectileConfig = {
        ammunition = weapon.currentStatistics.defaultAmmo,
        ballistics = {
          muzzleVelocity = weapon.currentStatistics.muzzleVelocity,
          mass = 4, -- grams (typical 5.56 bullet)
          diameter = 5.56, -- mm
          coefficientOfDrag = 0.3,
          ballisticCoefficient = 0.4,
          penetrationPower = 0.7,
          fragmentationChance = 0.1,
        },
        damage = weapon.currentStatistics.damage,
      }

      -- Create projectile in physics system
      local projectile =
        GSF.ProjectileSystem.createProjectile(projectileConfig, fireResult.origin, {
          X = direction.X * fireResult.velocity,
          Y = direction.Y * fireResult.velocity,
          Z = direction.Z * fireResult.velocity,
        })

      -- Publish weapon fired event
      GSF.WeaponEvents.publishWeaponFired(weapon, fireResult, nil)
    else
      warn(`[TestWeapon] Shot {i} failed: {fireResult.errorReason}`)
    end

    -- Small delay between shots
    task.wait(0.1)
  end

  -- Show weapon state after firing
  TestWeapon.showWeaponStatus(weapon)
end

function TestWeapon.demonstrateReloading(weapon: Weapon): ()
  print(`[TestWeapon] Demonstrating reloading with {weapon.name}`)

  -- Check current ammunition status
  print(
    `[TestWeapon] Before reload - Chambered: {weapon.state.ammunition.chambered}, Magazine: {weapon.state.ammunition.magazine}, Reserve: {weapon.state.ammunition.reserve}`
  )

  -- Perform tactical reload
  local reloadResult = weapon:reload(false)

  if reloadResult.success then
    print(`[TestWeapon] Reload successful: {reloadResult.reloadType}`)
    print(`  - Duration: {reloadResult.duration} seconds`)
    print(`  - Ammunition added: {reloadResult.ammunitionAdded}`)
    print(`  - Reserve consumed: {reloadResult.reserveConsumed}`)

    -- Publish weapon reloaded event
    GSF.WeaponEvents.publishWeaponReloaded(weapon, reloadResult, nil)

    -- Wait for reload to complete
    task.wait(reloadResult.duration)

    print(
      `[TestWeapon] After reload - Chambered: {weapon.state.ammunition.chambered}, Magazine: {weapon.state.ammunition.magazine}, Reserve: {weapon.state.ammunition.reserve}`
    )
  else
    warn(`[TestWeapon] Reload failed: {reloadResult.errorReason}`)
  end
end

function TestWeapon.demonstrateInspection(weapon: Weapon): ()
  print(`[TestWeapon] Demonstrating inspection with {weapon.name}`)

  local inspectionResult = weapon:inspect()

  print(`[TestWeapon] Inspection Results:`)
  print(`  - Chambered rounds: {inspectionResult.chamberedRounds}`)
  print(`  - Magazine rounds: {inspectionResult.magazineRounds}`)
  print(`  - Reserve rounds: {inspectionResult.reserveRounds}`)
  print(`  - Durability: {inspectionResult.durabilityPercent}%`)
  print(`  - Temperature: {inspectionResult.temperatureLevel}`)
  print(`  - Fouling: {inspectionResult.foulingLevel}`)
  print(`  - Jam risk: {inspectionResult.jamRisk}`)

  if inspectionResult.malfunctionWarnings then
    print(`  - Warnings:`)
    for _, warning in ipairs(inspectionResult.malfunctionWarnings) do
      print(`    * {warning}`)
    end
  end
end

function TestWeapon.showWeaponStatus(weapon: Weapon): ()
  print(`[TestWeapon] Weapon Status for {weapon.name}:`)
  print(`  - ID: {weapon.id}`)
  print(`  - Category: {weapon.category}`)
  print(
    `  - Ammunition: {weapon.state.ammunition.chambered}/{weapon.state.ammunition.magazine}/{weapon.state.ammunition.reserve}`
  )
  print(`  - Durability: {math.floor(weapon.state.condition.durability * 100)}%`)
  print(`  - Temperature: {math.floor(weapon.state.condition.temperature * 100)}%`)
  print(`  - Fire mode: {weapon.state.modes.fireMode}`)
  print(`  - Safety: {weapon.state.modes.safety and "ON" or "OFF"}`)
  print(`  - Can fire: {weapon:canFire() and "YES" or "NO"}`)
  print(`  - Can reload: {weapon:canReload() and "YES" or "NO"}`)

  -- Show attached components
  print(`  - Components:`)
  for componentType, component in pairs(weapon.components) do
    print(`    * {componentType}: {component.name} ({component.id})`)
  end
end

-- ============================================================================
-- COMPLETE TEST SUITE
-- ============================================================================

function TestWeapon.runCompleteTest(): ()
  print("=" .. string.rep("=", 60))
  print("Gun System Framework - Complete Test Suite")
  print("=" .. string.rep("=", 60))

  -- Initialize GSF if not already initialized
  if not GSF.isInitialized then
    print("[TestWeapon] Initializing GSF...")
    GSF.initialize()
    task.wait(1) -- Give systems time to initialize
  end

  -- Create test weapon
  print("\n[TestWeapon] Creating test weapon...")
  local weapon = TestWeapon.createBasicAssaultRifle()

  -- Attach components
  print("\n[TestWeapon] Attaching components...")
  TestWeapon.createTestComponents(weapon)

  -- Show initial status
  print("\n[TestWeapon] Initial weapon status:")
  TestWeapon.showWeaponStatus(weapon)

  -- Demonstrate inspection
  print("\n[TestWeapon] Testing inspection...")
  TestWeapon.demonstrateInspection(weapon)

  -- Demonstrate firing
  print("\n[TestWeapon] Testing firing mechanics...")
  TestWeapon.demonstrateFiring(weapon)

  -- Demonstrate reloading
  print("\n[TestWeapon] Testing reload mechanics...")
  TestWeapon.demonstrateReloading(weapon)

  -- Final status
  print("\n[TestWeapon] Final weapon status:")
  TestWeapon.showWeaponStatus(weapon)

  -- Show system statistics
  print("\n[TestWeapon] System Statistics:")
  local systemInfo = GSF.getSystemInfo()
  print(`  - GSF Version: {systemInfo.version}`)
  print(`  - Registered Factories: {#systemInfo.registeredFactories}`)
  print(`  - Active Projectiles: {GSF.ProjectileSystem.getProjectileCount()}`)
  print(`  - Memory Usage: {math.floor(systemInfo.memoryUsage)} KB`)

  local eventStats = GSF.EventBus.getStats()
  print(`  - Events Published: {eventStats.totalEventsPublished}`)
  print(`  - Events Processed: {eventStats.totalEventsProcessed}`)
  print(`  - Event Subscribers: {eventStats.subscriberCount}`)

  print("\n[TestWeapon] Test suite completed successfully!")
  print("=" .. string.rep("=", 60))
end

-- ============================================================================
-- MODULE EXPORT
-- ============================================================================

return TestWeapon
