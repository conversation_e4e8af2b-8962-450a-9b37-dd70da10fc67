--!strict
--[[
	Gun System Framework - Advanced Ballistics Test

	This module demonstrates the enhanced ballistics system including:
	- Environmental effects on projectile trajectory
	- Wind drift calculations
	- Bullet drop compensation
	- Penetration mechanics
	- Terminal ballistics
]]

local GSF = require(script.Parent.Parent.Core)

-- Type imports
local Types = require(script.Parent.Parent.Types)

local BallisticsTest = {}

-- Test configuration
local testConfig = {
  testRanges = { 100, 200, 300, 500, 800, 1000 }, -- meters
  testAmmunitionTypes = { "556x45", "762x51", "308win", "50bmg" },
  testEnvironments = { "desert", "arctic", "tropical", "mountain" },
  windSpeeds = { 0, 5, 10, 15, 20 }, -- m/s
}

-- ============================================================================
-- BALLISTICS TESTING
-- ============================================================================

function BallisticsTest.runComprehensiveBallisticsTest(): ()
  print("\n🎯 === ADVANCED BALLISTICS SYSTEM TEST ===")
  print("Testing environmental effects, wind drift, and penetration...")

  -- Test 1: Environmental effects
  print("\n--- Test 1: Environmental Effects ---")
  BallisticsTest.testEnvironmentalEffects()

  -- Test 2: Wind drift calculations
  print("\n--- Test 2: Wind Drift Analysis ---")
  BallisticsTest.testWindDrift()

  -- Test 3: Bullet drop compensation
  print("\n--- Test 3: Bullet Drop Compensation ---")
  BallisticsTest.testBulletDrop()

  -- Test 4: Penetration mechanics
  print("\n--- Test 4: Penetration Mechanics ---")
  BallisticsTest.testPenetrationMechanics()

  -- Test 5: Terminal ballistics
  print("\n--- Test 5: Terminal Ballistics ---")
  BallisticsTest.testTerminalBallistics()

  -- Test 6: Trajectory comparison
  print("\n--- Test 6: Trajectory Comparison ---")
  BallisticsTest.testTrajectoryComparison()

  print("\n✅ === BALLISTICS TESTS COMPLETE ===")
end

function BallisticsTest.testEnvironmentalEffects(): ()
  print("Testing environmental effects on ballistics...")

  -- Test different environmental conditions
  for _, environment in ipairs(testConfig.testEnvironments) do
    print(`\n  Testing {environment} environment:`)

    -- Apply environmental preset
    GSF.EnvironmentalSystem.applyWeatherPreset(environment)
    local conditions = GSF.EnvironmentalSystem.getCurrentConditions()

    -- Calculate ballistic corrections
    local corrections = GSF.EnvironmentalSystem.calculateBallisticCorrections(500)

    print(`    Temperature: {math.floor(conditions.temperature)}°C`)
    print(`    Humidity: {math.floor(conditions.humidity * 100)}%`)
    print(`    Wind: {math.floor(math.sqrt(conditions.wind.X ^ 2 + conditions.wind.Z ^ 2))}m/s`)
    print(`    Ballistic correction: {math.floor(corrections.total * 100)}%`)

    -- Test projectile behavior
    local projectile = {
      position = { X = 0, Y = 100, Z = 0 },
      velocity = { X = 0, Y = 0, Z = -900 }, -- 900 m/s muzzle velocity
      mass = 0.004, -- 4g projectile
      diameter = 0.00556, -- 5.56mm
      ammunitionType = "556x45",
    }

    -- Calculate trajectory with environmental effects
    local trajectory = GSF.AdvancedBallistics.calculateTrajectory(projectile, conditions, 0.01, 2.0)

    if #trajectory > 0 then
      local finalPoint = trajectory[#trajectory]
      print(`    Range achieved: {math.floor(math.abs(finalPoint.Z))}m`)
      print(`    Drop at 500m: {math.floor(finalPoint.Y - 100)}m`)
    end
  end
end

function BallisticsTest.testWindDrift(): ()
  print("Testing wind drift calculations...")

  -- Test different wind speeds
  for _, windSpeed in ipairs(testConfig.windSpeeds) do
    print(`\n  Wind speed: {windSpeed}m/s`)

    -- Set crosswind
    GSF.EnvironmentalSystem.setWind(90, windSpeed) -- 90° = crosswind

    -- Test different ammunition types
    for _, ammoType in ipairs(testConfig.testAmmunitionTypes) do
      local muzzleVelocity = BallisticsTest._getMuzzleVelocity(ammoType)
      local windDrift = GSF.AdvancedBallistics.calculateWindDrift(
        muzzleVelocity,
        500, -- 500m range
        { X = windSpeed, Y = 0, Z = 0 }, -- crosswind
        ammoType
      )

      print(`    {ammoType}: {math.floor(windDrift.X * 100)}cm drift at 500m`)
    end
  end
end

function BallisticsTest.testBulletDrop(): ()
  print("Testing bullet drop calculations...")

  -- Test different ranges and ammunition types
  for _, ammoType in ipairs(testConfig.testAmmunitionTypes) do
    print(`\n  Ammunition: {ammoType}`)

    local muzzleVelocity = BallisticsTest._getMuzzleVelocity(ammoType)
    local conditions = GSF.EnvironmentalSystem.getCurrentConditions()

    for _, range in ipairs(testConfig.testRanges) do
      local drop =
        GSF.AdvancedBallistics.calculateBulletDrop(muzzleVelocity, range, ammoType, conditions)

      print(`    {range}m: {math.floor(drop * 100)}cm drop`)
    end
  end
end

function BallisticsTest.testPenetrationMechanics(): ()
  print("Testing penetration mechanics...")

  -- Test materials
  local materials = { "Wood", "Concrete", "Metal", "Glass" }
  local thicknesses = { 5, 10, 20, 50 } -- cm

  for _, material in ipairs(materials) do
    print(`\n  Material: {material}`)

    for _, thickness in ipairs(thicknesses) do
      -- Create test projectile
      local projectile = {
        position = { X = 0, Y = 0, Z = 0 },
        velocity = { X = 0, Y = 0, Z = -800 }, -- 800 m/s
        mass = 0.004, -- 4g
        diameter = 0.00556, -- 5.56mm
        ammunitionType = "556x45",
      }

      -- Test penetration at 0° angle
      local result = GSF.AdvancedBallistics.calculatePenetration(
        projectile,
        material,
        thickness / 100, -- Convert cm to meters
        0 -- perpendicular impact
      )

      local status = result.penetrated and "PENETRATED" or "STOPPED"
      local energyLoss = math.floor(result.energyLoss * 100)

      print(`    {thickness}cm: {status} ({energyLoss}% energy loss)`)
    end
  end
end

function BallisticsTest.testTerminalBallistics(): ()
  print("Testing terminal ballistics...")

  -- Test different ammunition types
  for _, ammoType in ipairs(testConfig.testAmmunitionTypes) do
    print(`\n  Ammunition: {ammoType}`)

    local muzzleVelocity = BallisticsTest._getMuzzleVelocity(ammoType)
    local mass = BallisticsTest._getProjectileMass(ammoType)

    -- Create projectile at different velocities (simulating range)
    local velocities = { muzzleVelocity, muzzleVelocity * 0.8, muzzleVelocity * 0.6 }
    local ranges = { "Close", "Medium", "Long" }

    for i, velocity in ipairs(velocities) do
      local projectile = {
        position = { X = 0, Y = 0, Z = 0 },
        velocity = { X = 0, Y = 0, Z = -velocity },
        mass = mass,
        diameter = BallisticsTest._getProjectileDiameter(ammoType),
        ammunitionType = ammoType,
      }

      local terminal = GSF.AdvancedBallistics.calculateTerminalEffects(projectile, {})

      print(`    {ranges[i]} range ({math.floor(velocity)}m/s):`)
      print(`      Damage: {math.floor(terminal.damage)}`)
      print(`      Energy transfer: {math.floor(terminal.energyTransfer)}J`)
      print(`      Stopping power: {math.floor(terminal.stoppingPower * 100)}`)
    end
  end
end

function BallisticsTest.testTrajectoryComparison(): ()
  print("Testing trajectory comparison between ammunition types...")

  -- Compare trajectories at 500m
  local range = 500
  local conditions = GSF.EnvironmentalSystem.getCurrentConditions()

  print(`\n  Trajectory comparison at {range}m:`)
  print("  Ammo Type    | Velocity | Drop    | Time   | Energy")
  print("  -------------|----------|---------|--------|--------")

  for _, ammoType in ipairs(testConfig.testAmmunitionTypes) do
    local muzzleVelocity = BallisticsTest._getMuzzleVelocity(ammoType)
    local mass = BallisticsTest._getProjectileMass(ammoType)

    -- Calculate bullet drop
    local drop =
      GSF.AdvancedBallistics.calculateBulletDrop(muzzleVelocity, range, ammoType, conditions)

    -- Calculate time of flight (simplified)
    local timeOfFlight = range / (muzzleVelocity * 0.85) -- Account for drag

    -- Calculate remaining energy (simplified)
    local remainingVelocity = muzzleVelocity * 0.7 -- Simplified drag effect
    local remainingEnergy = 0.5 * mass * remainingVelocity ^ 2

    print(
      `  {ammoType} | {math.floor(muzzleVelocity)} m/s | {math.floor(drop * 100)}cm | {math.floor(
        timeOfFlight * 1000
      )}ms | {math.floor(remainingEnergy)}J`
    )
  end
end

-- ============================================================================
-- REAL-WORLD BALLISTICS DEMONSTRATION
-- ============================================================================

function BallisticsTest.demonstrateRealWorldScenario(): ()
  print("\n🌍 === REAL-WORLD BALLISTICS SCENARIO ===")
  print("Simulating long-range precision shooting...")

  -- Set up mountain environment
  GSF.EnvironmentalSystem.applyWeatherPreset("mountain")
  GSF.EnvironmentalSystem.setAltitude(1500) -- 1500m altitude
  GSF.EnvironmentalSystem.setWind(45, 8) -- 8 m/s wind at 45°

  local conditions = GSF.EnvironmentalSystem.getCurrentConditions()
  print(`Environment: {conditions.weatherType}`)
  print(`Altitude: {conditions.altitude}m`)
  print(`Temperature: {math.floor(conditions.temperature)}°C`)
  print(`Wind: {math.floor(math.sqrt(conditions.wind.X ^ 2 + conditions.wind.Z ^ 2))}m/s`)

  -- Create precision rifle setup
  local weapon = BallisticsTest._createPrecisionRifle()

  -- Test shots at various ranges
  local targets = {
    { range = 300, name = "Close target" },
    { range = 600, name = "Medium target" },
    { range = 1000, name = "Long target" },
    { range = 1500, name = "Extreme target" },
  }

  print("\nShot analysis:")
  for _, target in ipairs(targets) do
    print(`\n{target.name} ({target.range}m):`)

    -- Calculate corrections needed
    local drop = GSF.AdvancedBallistics.calculateBulletDrop(850, target.range, "308win", conditions)
    local windDrift =
      GSF.AdvancedBallistics.calculateWindDrift(850, target.range, conditions.wind, "308win")
    local corrections = GSF.EnvironmentalSystem.calculateBallisticCorrections(target.range)

    print(`  Bullet drop: {math.floor(drop * 100)}cm`)
    print(`  Wind drift: {math.floor(windDrift.X * 100)}cm`)
    print(`  Environmental correction: {math.floor(corrections.total * 100)}%`)

    -- Calculate scope adjustments (MOA)
    local dropMOA = (drop / target.range) * 3437.75 -- Convert to MOA
    local driftMOA = (windDrift.X / target.range) * 3437.75

    print(
      `  Scope adjustment: {math.floor(dropMOA * 4)} clicks up, {math.floor(driftMOA * 4)} clicks left`
    )
  end
end

-- ============================================================================
-- HELPER FUNCTIONS
-- ============================================================================

function BallisticsTest._getMuzzleVelocity(ammoType: string): number
  local velocities = {
    ["556x45"] = 990, -- m/s
    ["762x51"] = 850, -- m/s
    ["308win"] = 850, -- m/s
    ["50bmg"] = 900, -- m/s
    ["9x19"] = 350, -- m/s
  }
  return velocities[ammoType] or 800
end

function BallisticsTest._getProjectileMass(ammoType: string): number
  local masses = {
    ["556x45"] = 0.004, -- 4g
    ["762x51"] = 0.0095, -- 9.5g
    ["308win"] = 0.0095, -- 9.5g
    ["50bmg"] = 0.042, -- 42g
    ["9x19"] = 0.008, -- 8g
  }
  return masses[ammoType] or 0.004
end

function BallisticsTest._getProjectileDiameter(ammoType: string): number
  local diameters = {
    ["556x45"] = 0.00556, -- 5.56mm
    ["762x51"] = 0.00762, -- 7.62mm
    ["308win"] = 0.00762, -- 7.62mm
    ["50bmg"] = 0.0127, -- 12.7mm
    ["9x19"] = 0.009, -- 9mm
  }
  return diameters[ammoType] or 0.00556
end

function BallisticsTest._createPrecisionRifle(): Weapon
  -- Create a precision rifle for ballistics testing
  local weapon = GSF.WeaponEntity.new({
    id = "precision_rifle_test",
    name = "Precision Test Rifle",
    category = "SniperRifle",
    baseStatistics = {
      damage = 85,
      accuracy = 0.98,
      range = 1500,
      muzzleVelocity = 850,
      defaultAmmo = "308win",
    },
  })

  -- Add precision components
  local scope = GSF.createSight({
    magnification = 10.0,
    reticleType = "Mil-Dot",
    zeroRange = 300,
  })

  local precisionStock = GSF.createStock({
    stockType = "Fixed",
    recoilReduction = 0.25,
  })

  -- Attach components (simplified for testing)
  -- In real implementation, would need proper attachment points

  return weapon
end

return BallisticsTest
