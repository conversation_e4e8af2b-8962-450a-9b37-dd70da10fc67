--!strict
--[[
	Gun System Framework - UI System Test

	This module demonstrates the weapon customization UI system:
	- Opening and closing the customization interface
	- 3D weapon inspection and rotation
	- Real-time statistics visualization
	- Component attachment/detachment
	- User interaction testing
]]

local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")

local AdvancedComponentTest = require(script.Parent.AdvancedComponentTest)
local GSF = require(script.Parent.Parent.Core)

-- Type imports
local Types = require(script.Parent.Parent.Types)

local UITest = {}

-- Test state
local testState = {
  currentWeapon = nil,
  testComponents = {},
  uiTestActive = false,
}

-- ============================================================================
-- UI TESTING FUNCTIONS
-- ============================================================================

function UITest.runUISystemTest(): ()
  print("\n🖥️ === WEAPON CUSTOMIZATION UI TEST ===")
  print("Testing user interface system...")

  -- Test 1: UI initialization
  print("\n--- Test 1: UI System Initialization ---")
  UITest.testUIInitialization()

  -- Test 2: Create test weapon
  print("\n--- Test 2: Create Test Weapon ---")
  local weapon = UITest.createTestWeapon()
  testState.currentWeapon = weapon

  -- Test 3: Open customization UI
  print("\n--- Test 3: Open Customization UI ---")
  UITest.testOpenUI(weapon)

  -- Test 4: UI interaction simulation
  print("\n--- Test 4: UI Interaction Simulation ---")
  UITest.simulateUIInteractions()

  -- Test 5: Component management
  print("\n--- Test 5: Component Management ---")
  UITest.testComponentManagement()

  -- Test 6: Statistics visualization
  print("\n--- Test 6: Statistics Visualization ---")
  UITest.testStatisticsVisualization()

  print("\n--- UI Test Instructions ---")
  UITest.printUIInstructions()

  print("\n✅ === UI SYSTEM TESTS COMPLETE ===")
end

function UITest.testUIInitialization(): ()
  print("Testing UI system initialization...")

  -- Check if UI system is available
  if GSF.WeaponCustomizationUI then
    print("  ✅ WeaponCustomizationUI module loaded")

    -- Test UI state
    local isOpen = GSF.WeaponCustomizationUI.isUIOpen()
    print(`  ✅ UI state check: {isOpen and "Open" or "Closed"}`)
  else
    warn("  ❌ WeaponCustomizationUI module not found")
  end

  -- Check if running on client
  if game:GetService("RunService"):IsClient() then
    print("  ✅ Running on client - UI available")
  else
    print("  ⚠️ Running on server - UI not available")
  end
end

function UITest.createTestWeapon(): Weapon
  print("Creating test weapon for UI demonstration...")

  -- Create a tactical rifle
  local weapon = AdvancedComponentTest.createTacticalRifle()

  -- Add some components for testing
  local barrel = GSF.createBarrel({
    id = "test_barrel_ui",
    name = '16" Test Barrel',
    length = 16,
    bore = 5.56,
    rifling = "Standard",
  })

  local magazine = GSF.createMagazine({
    id = "test_magazine_ui",
    name = "30-Round Test Magazine",
    capacity = 30,
    compatibleAmmo = { "556x45" },
  })

  -- Attach basic components
  weapon:attach(barrel, "barrel_mount")
  weapon:attach(magazine, "magazine_well")

  print(`  ✅ Created test weapon: {weapon.name}`)
  print(`  ✅ Attached {#weapon.attachedComponents} components`)

  return weapon
end

function UITest.testOpenUI(weapon: Weapon): ()
  print("Testing UI opening and closing...")

  if not GSF.WeaponCustomizationUI then
    warn("  ❌ UI system not available")
    return
  end

  -- Test opening UI
  GSF.WeaponCustomizationUI.openUI(weapon)

  if GSF.WeaponCustomizationUI.isUIOpen() then
    print("  ✅ UI opened successfully")
    testState.uiTestActive = true
  else
    warn("  ❌ Failed to open UI")
  end

  -- Wait a moment then test closing
  task.wait(2)

  print("  Testing UI close...")
  GSF.WeaponCustomizationUI.closeUI()

  if not GSF.WeaponCustomizationUI.isUIOpen() then
    print("  ✅ UI closed successfully")
  else
    warn("  ❌ Failed to close UI")
  end

  -- Reopen for further testing
  GSF.WeaponCustomizationUI.openUI(weapon)
  testState.uiTestActive = true
end

function UITest.simulateUIInteractions(): ()
  print("Simulating UI interactions...")

  if not testState.uiTestActive then
    print("  ⚠️ UI not active - skipping interaction tests")
    return
  end

  -- Simulate weapon rotation (would be done by user mouse input)
  print("  Simulating weapon rotation...")

  -- Simulate component selection
  print("  Simulating component selection...")

  -- Simulate attachment point highlighting
  print("  Simulating attachment point interaction...")

  print("  ✅ UI interaction simulation complete")
end

function UITest.testComponentManagement(): ()
  print("Testing component management through UI...")

  if not testState.currentWeapon then
    warn("  ❌ No test weapon available")
    return
  end

  local weapon = testState.currentWeapon

  -- Create test components
  local sight = GSF.createSight({
    id = "test_sight_ui",
    name = "Test Red Dot",
    magnification = 1.0,
    reticleType = "Dot",
  })

  local stock = GSF.createStock({
    id = "test_stock_ui",
    name = "Test Fixed Stock",
    stockType = "Fixed",
    lengthOfPull = 350,
  })

  -- Test component attachment
  print("  Testing component attachment...")
  local attachResult = weapon:attach(sight, "optic_rail")
  if attachResult.success then
    print(`    ✅ Attached {sight.name}`)
  else
    warn(`    ❌ Failed to attach sight: {attachResult.errorReason}`)
  end

  attachResult = weapon:attach(stock, "stock_mount")
  if attachResult.success then
    print(`    ✅ Attached {stock.name}`)
  else
    warn(`    ❌ Failed to attach stock: {attachResult.errorReason}`)
  end

  -- Test component detachment
  print("  Testing component detachment...")
  local detachResult = weapon:detach(sight)
  if detachResult.success then
    print(`    ✅ Detached {sight.name}`)
  else
    warn(`    ❌ Failed to detach sight: {detachResult.errorReason}`)
  end

  -- Reattach for statistics test
  weapon:attach(sight, "optic_rail")
end

function UITest.testStatisticsVisualization(): ()
  print("Testing statistics visualization...")

  if not testState.currentWeapon then
    warn("  ❌ No test weapon available")
    return
  end

  local weapon = testState.currentWeapon

  -- Get base and effective statistics
  local baseStats = weapon.baseStatistics
  local effectiveStats = weapon:getEffectiveStatistics()

  print("  Base vs Effective Statistics:")
  print(`    Damage: {baseStats.damage} → {effectiveStats.damage}`)
  print(
    `    Accuracy: {math.floor(baseStats.accuracy * 100)}% → {math.floor(
      effectiveStats.accuracy * 100
    )}%`
  )
  print(`    Range: {baseStats.range}m → {effectiveStats.range}m`)
  print(
    `    Recoil: {math.floor(baseStats.recoil.vertical * 100)} → {math.floor(
      effectiveStats.recoil.vertical * 100
    )}`
  )

  -- Calculate improvements
  local accuracyImprovement = (effectiveStats.accuracy - baseStats.accuracy)
    / baseStats.accuracy
    * 100
  local recoilImprovement = (baseStats.recoil.vertical - effectiveStats.recoil.vertical)
    / baseStats.recoil.vertical
    * 100

  print("  Component Effects:")
  print(`    Accuracy improvement: {math.floor(accuracyImprovement * 10) / 10}%`)
  print(`    Recoil reduction: {math.floor(recoilImprovement * 10) / 10}%`)

  print("  ✅ Statistics visualization test complete")
end

function UITest.printUIInstructions(): ()
  print("UI Test Instructions for Manual Testing:")
  print("  1. The customization UI should be open")
  print("  2. Try the following interactions:")
  print("     • Click and drag in the weapon viewport to rotate the weapon")
  print("     • Press 'R' to reset the weapon view")
  print("     • Click on attachment points (colored spheres) to select them")
  print("     • Use the 'Attach' buttons to attach components")
  print("     • Use the 'Remove' buttons to detach components")
  print("     • Watch the statistics panel update in real-time")
  print("     • Press 'Escape' or click 'Close' to close the UI")
  print("  3. Observe the real-time stat changes as you attach/detach components")
end

-- ============================================================================
-- INTERACTIVE UI DEMO
-- ============================================================================

function UITest.startInteractiveDemo(): ()
  print("\n🎮 === INTERACTIVE UI DEMO ===")
  print("Starting interactive weapon customization demo...")

  -- Create a fully configured weapon
  local weapon = AdvancedComponentTest.createTacticalRifle()
  AdvancedComponentTest.attachTacticalComponents(weapon)

  -- Open the UI
  if GSF.WeaponCustomizationUI then
    GSF.WeaponCustomizationUI.openUI(weapon)
    print("✅ Interactive demo started!")
    print("Use the UI to customize your weapon.")
    print("Press 'Q' to close the demo.")

    -- Set up demo controls
    UITest._setupDemoControls(weapon)
  else
    warn("❌ UI system not available for interactive demo")
  end
end

function UITest._setupDemoControls(weapon: Weapon): ()
  -- Set up keyboard controls for the demo
  local connection
  connection = UserInputService.InputBegan:Connect(function(input, gameProcessed)
    if gameProcessed then
      return
    end

    if input.KeyCode == Enum.KeyCode.Q then
      -- Close demo
      if GSF.WeaponCustomizationUI then
        GSF.WeaponCustomizationUI.closeUI()
      end
      connection:Disconnect()
      print("Interactive demo closed.")
    elseif input.KeyCode == Enum.KeyCode.T then
      -- Toggle different weapon configurations
      UITest._cycleWeaponConfiguration(weapon)
    elseif input.KeyCode == Enum.KeyCode.E then
      -- Show weapon statistics
      UITest._showWeaponStats(weapon)
    end
  end)

  print("Demo Controls:")
  print("  Q - Close demo")
  print("  T - Cycle weapon configurations")
  print("  E - Show detailed weapon statistics")
end

function UITest._cycleWeaponConfiguration(weapon: Weapon): ()
  -- Cycle through different component configurations
  local configurations = {
    "CQB",
    "Marksman",
    "Stealth",
    "Heavy",
  }

  local currentConfig = weapon.customData and weapon.customData.configuration or "CQB"
  local currentIndex = 1

  for i, config in ipairs(configurations) do
    if config == currentConfig then
      currentIndex = i
      break
    end
  end

  local nextIndex = (currentIndex % #configurations) + 1
  local nextConfig = configurations[nextIndex]

  print(`Switching to {nextConfig} configuration...`)
  UITest._applyConfiguration(weapon, nextConfig)

  -- Store current configuration
  weapon.customData = weapon.customData or {}
  weapon.customData.configuration = nextConfig
end

function UITest._applyConfiguration(weapon: Weapon, configName: string): ()
  -- Apply different component configurations
  if configName == "CQB" then
    -- Close quarters battle setup
    print("  Applying CQB configuration: Red dot, short barrel, vertical grip")
  elseif configName == "Marksman" then
    -- Precision shooting setup
    print("  Applying Marksman configuration: Magnified scope, precision stock")
  elseif configName == "Stealth" then
    -- Suppressed setup
    print("  Applying Stealth configuration: Suppressor, subsonic ammo")
  elseif configName == "Heavy" then
    -- Heavy assault setup
    print("  Applying Heavy configuration: Bipod, heavy barrel, high-capacity magazine")
  end
end

function UITest._showWeaponStats(weapon: Weapon): ()
  local stats = weapon:getEffectiveStatistics()

  print("\n📊 Detailed Weapon Statistics:")
  print(`  Name: {weapon.name}`)
  print(`  Category: {weapon.category}`)
  print(`  Damage: {stats.damage}`)
  print(`  Accuracy: {math.floor(stats.accuracy * 100)}%`)
  print(`  Range: {stats.range}m`)
  print(`  Fire Rate: {stats.fireRate} RPM`)
  print(`  Muzzle Velocity: {stats.muzzleVelocity} m/s`)
  print(
    `  Recoil (V/H): {math.floor(stats.recoil.vertical * 100)}/{math.floor(
      stats.recoil.horizontal * 100
    )}`
  )
  print(`  Reliability: {math.floor(stats.reliability * 100)}%`)

  -- Component breakdown
  print("\n🔧 Attached Components:")
  for componentType, component in pairs(weapon.attachedComponents) do
    print(`  {componentType}: {component.name}`)
  end
end

-- ============================================================================
-- QUICK UI TESTS
-- ============================================================================

function UITest.quickUITest(): ()
  print("\n⚡ Quick UI Test")

  -- Create simple weapon
  local weapon = GSF.WeaponEntity.new({
    id = "quick_test_weapon",
    name = "Quick Test Rifle",
    category = "AssaultRifle",
    baseStatistics = {
      damage = 35,
      accuracy = 0.8,
      range = 500,
      fireRate = 650,
    },
  })

  -- Add attachment points
  weapon.attachmentPoints.optic_rail = {
    name = "optic_rail",
    position = { X = 0, Y = 0.1, Z = -0.2 },
    occupied = false,
    component = nil,
  }

  -- Test UI open/close
  if GSF.WeaponCustomizationUI then
    GSF.WeaponCustomizationUI.openUI(weapon)
    print("  ✅ Quick UI test: UI opened")

    task.wait(1)

    GSF.WeaponCustomizationUI.closeUI()
    print("  ✅ Quick UI test: UI closed")
  else
    warn("  ❌ UI system not available")
  end
end

return UITest
