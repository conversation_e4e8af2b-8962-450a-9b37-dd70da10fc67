--!strict
--[[
	Gun System Framework - Integration Test

	This module provides comprehensive testing of all GSF systems working together:
	- Advanced component system
	- Visual effects system
	- Audio system
	- Ballistics and physics
	- Performance monitoring
]]

local AdvancedComponentTest = require(script.Parent.AdvancedComponentTest)
local GSF = require(script.Parent.Parent.Core)

-- Type imports
local Types = require(script.Parent.Parent.Types)

local IntegrationTest = {}

-- Test configuration
local testConfig = {
  enableVisualEffects = true,
  enableAudioEffects = true,
  enablePerformanceMonitoring = true,
  testDuration = 30, -- seconds
  fireRate = 2, -- shots per second
}

-- Performance tracking
local performanceData = {
  frameRate = {},
  memoryUsage = {},
  effectsCount = 0,
  audioSourcesCount = 0,
  startTime = 0,
}

-- ============================================================================
-- MAIN INTEGRATION TEST
-- ============================================================================

function IntegrationTest.runFullSystemTest(): ()
  print("\n🔫 === GSF FULL SYSTEM INTEGRATION TEST ===")
  print("Testing all systems working together...")

  -- Initialize performance monitoring
  IntegrationTest._startPerformanceMonitoring()

  -- Test 1: System initialization
  print("\n--- Test 1: System Initialization ---")
  IntegrationTest.testSystemInitialization()

  -- Test 2: Advanced weapon creation
  print("\n--- Test 2: Advanced Weapon Creation ---")
  local tacticalRifle = IntegrationTest.createFullyConfiguredWeapon()

  -- Test 3: Component interaction
  print("\n--- Test 3: Component Interaction ---")
  IntegrationTest.testComponentInteraction(tacticalRifle)

  -- Test 4: Effects and audio integration
  print("\n--- Test 4: Effects and Audio Integration ---")
  IntegrationTest.testEffectsAndAudio(tacticalRifle)

  -- Test 5: Performance under load
  print("\n--- Test 5: Performance Under Load ---")
  IntegrationTest.testPerformanceUnderLoad(tacticalRifle)

  -- Test 6: Component degradation and maintenance
  print("\n--- Test 6: Component Degradation ---")
  IntegrationTest.testComponentDegradation(tacticalRifle)

  -- Generate final report
  print("\n--- Final Performance Report ---")
  IntegrationTest._generatePerformanceReport()

  print("\n✅ === INTEGRATION TEST COMPLETE ===")
end

function IntegrationTest.testSystemInitialization(): ()
  print("Testing GSF system initialization...")

  -- Check if all systems are initialized
  local systems = {
    "ComponentRegistry",
    "EventBus",
    "ProjectileSystem",
    "EffectsSystem",
    "AudioSystem",
  }

  for _, systemName in ipairs(systems) do
    local system = GSF[systemName]
    if system then
      print(`  ✓ {systemName} initialized`)
    else
      warn(`  ❌ {systemName} not found`)
    end
  end

  -- Test component factory registration
  local factories = GSF.ComponentRegistry.getRegisteredFactories()
  print(`  ✓ {#factories} component factories registered`)

  -- Test event bus
  local testEventReceived = false
  GSF.EventBus.subscribe("TestEvent", function()
    testEventReceived = true
  end)
  GSF.EventBus.emit("TestEvent", {})

  if testEventReceived then
    print("  ✓ Event bus working")
  else
    warn("  ❌ Event bus not working")
  end
end

function IntegrationTest.createFullyConfiguredWeapon(): Weapon
  print("Creating fully configured tactical rifle...")

  -- Create tactical rifle with all advanced components
  local weapon = AdvancedComponentTest.createTacticalRifle()
  AdvancedComponentTest.attachTacticalComponents(weapon)

  -- Verify all components are attached
  local componentCount = 0
  for componentType, component in pairs(weapon.attachedComponents) do
    componentCount += 1
    print(`  ✓ {componentType}: {component.name}`)
  end

  print(`  Total components attached: {componentCount}`)

  -- Test weapon statistics calculation
  local stats = weapon:getEffectiveStatistics()
  print(`  Effective accuracy: {math.floor(stats.accuracy * 100)}%`)
  print(`  Effective range: {stats.range}m`)
  print(`  Recoil reduction: {math.floor((1 - stats.recoil.vertical) * 100)}%`)

  return weapon
end

function IntegrationTest.testComponentInteraction(weapon: Weapon): ()
  print("Testing component interaction and synergy...")

  -- Test sight and suppressor interaction
  local sight = weapon:getComponent("Sight")
  local suppressor = weapon:getComponent("Suppressor")

  if sight and suppressor then
    -- Test ballistic calculation with both components
    local baseAccuracy = weapon.baseStatistics.accuracy
    local modifiedAccuracy = sight:modifyAimingAccuracy(baseAccuracy, 200)

    print(`  Sight accuracy modification: +{math.floor((modifiedAccuracy - baseAccuracy) * 100)}%`)

    -- Test suppressor sound modification
    local baseSound = {
      volume = 1.0,
      pitch = 1.0,
      soundId = "test_sound",
    }
    local suppressedSound = suppressor:modifySound(baseSound)

    print(`  Sound suppression: -{math.floor((1 - suppressedSound.volume) * 100)}%`)
  end

  -- Test stock and grip synergy
  local stock = weapon:getComponent("Stock")
  local grip = weapon:getComponent("Grip")

  if stock and grip then
    local baseRecoil = { X = 1.0, Y = 1.0, Z = 0 }
    local stockModified = stock:modifyRecoil(baseRecoil)
    local totalReduction = (baseRecoil.Y - stockModified.Y) / baseRecoil.Y

    print(`  Combined recoil reduction: {math.floor(totalReduction * 100)}%`)
  end
end

function IntegrationTest.testEffectsAndAudio(weapon: Weapon): ()
  print("Testing visual effects and audio integration...")

  -- Test firing with effects
  local fireCount = 5
  for i = 1, fireCount do
    local fireResult = weapon:fire(
      { X = 0, Y = 5, Z = 0 }, -- origin
      { X = 0, Y = 0, Z = -1 } -- direction
    )

    if fireResult.success then
      print(`  Shot {i}: Effects and audio triggered`)

      -- Track effects
      performanceData.effectsCount += 2 -- muzzle flash + shell ejection
      performanceData.audioSourcesCount += 1

      -- Wait between shots
      task.wait(0.5)
    else
      warn(`  Shot {i} failed: {fireResult.errorReason}`)
    end
  end

  -- Test reload with audio
  print("  Testing reload audio...")
  local reloadResult = weapon:reload(true)
  if reloadResult.success then
    print(`  Reload completed: {reloadResult.reloadType} reload`)
    performanceData.audioSourcesCount += 3 -- reload sequence sounds
  end
end

function IntegrationTest.testPerformanceUnderLoad(weapon: Weapon): ()
  print("Testing performance under sustained fire...")

  local startTime = tick()
  local shotsFired = 0
  local targetDuration = 10 -- seconds

  while tick() - startTime < targetDuration do
    local fireResult = weapon:fire(
      { X = 0, Y = 5, Z = 0 },
      { X = math.random() - 0.5, Y = math.random() - 0.5, Z = -1 }
    )

    if fireResult.success then
      shotsFired += 1
      performanceData.effectsCount += 2
      performanceData.audioSourcesCount += 1
    end

    -- Maintain fire rate
    task.wait(1 / testConfig.fireRate)
  end

  local actualDuration = tick() - startTime
  local actualFireRate = shotsFired / actualDuration

  print(`  Shots fired: {shotsFired}`)
  print(`  Actual fire rate: {math.floor(actualFireRate * 10) / 10} shots/sec`)
  print(`  Target fire rate: {testConfig.fireRate} shots/sec`)

  -- Check performance impact
  local memoryUsage = collectgarbage("count")
  print(`  Memory usage: {math.floor(memoryUsage)} KB`)
end

function IntegrationTest.testComponentDegradation(weapon: Weapon): ()
  print("Testing component degradation over time...")

  -- Simulate extended use
  local extendedFireCount = 200

  for i = 1, extendedFireCount do
    weapon:fire({ X = 0, Y = 5, Z = 0 }, { X = 0, Y = 0, Z = -1 })

    -- Occasional status check
    if i % 50 == 0 then
      local suppressor = weapon:getComponent("Suppressor")
      if suppressor then
        local condition = suppressor:getConditionReport()
        print(
          `  After {i} shots - Carbon: {condition.carbonBuildup}%, Temp: {condition.currentTemperature}°C`
        )
      end
    end
  end

  -- Final condition report
  print("  Final component conditions:")
  for componentType, component in pairs(weapon.attachedComponents) do
    if component.getConditionReport then
      local condition = component:getConditionReport()
      print(`    {componentType}: {condition.carbonBuildup or 0}% wear`)
    end
  end
end

-- ============================================================================
-- PERFORMANCE MONITORING
-- ============================================================================

function IntegrationTest._startPerformanceMonitoring(): ()
  performanceData.startTime = tick()

  -- Monitor frame rate and memory usage
  local connection
  connection = game:GetService("RunService").Heartbeat:Connect(function()
    local currentTime = tick()
    if currentTime - performanceData.startTime > testConfig.testDuration then
      connection:Disconnect()
      return
    end

    -- Track frame rate (simplified)
    table.insert(performanceData.frameRate, 1 / game:GetService("RunService").Heartbeat:Wait())

    -- Track memory usage
    table.insert(performanceData.memoryUsage, collectgarbage("count"))
  end)
end

function IntegrationTest._generatePerformanceReport(): ()
  local duration = tick() - performanceData.startTime

  -- Calculate averages
  local avgFrameRate = 0
  for _, fps in ipairs(performanceData.frameRate) do
    avgFrameRate += fps
  end
  avgFrameRate = avgFrameRate / #performanceData.frameRate

  local avgMemory = 0
  for _, memory in ipairs(performanceData.memoryUsage) do
    avgMemory += memory
  end
  avgMemory = avgMemory / #performanceData.memoryUsage

  -- Generate report
  print("📊 PERFORMANCE REPORT:")
  print(`  Test duration: {math.floor(duration * 10) / 10}s`)
  print(`  Average FPS: {math.floor(avgFrameRate)}`)
  print(`  Average memory: {math.floor(avgMemory)} KB`)
  print(`  Effects created: {performanceData.effectsCount}`)
  print(`  Audio sources: {performanceData.audioSourcesCount}`)

  -- Performance assessment
  if avgFrameRate > 50 then
    print("  ✅ Performance: Excellent")
  elseif avgFrameRate > 30 then
    print("  ✅ Performance: Good")
  else
    print("  ⚠️ Performance: Needs optimization")
  end
end

-- ============================================================================
-- QUICK TESTS
-- ============================================================================

function IntegrationTest.quickComponentTest(): ()
  print("\n🔧 Quick Component Test")

  -- Test each component type individually
  local componentTests = {
    { type = "Sight", config = { magnification = 4.0, reticleType = "ACOG" } },
    { type = "Stock", config = { stockType = "Fixed", lengthOfPull = 350 } },
    { type = "Grip", config = { gripType = "Vertical", textureType = "Stippled" } },
    { type = "Suppressor", config = { suppressorType = "Baffle", dbReduction = 30 } },
  }

  for _, test in ipairs(componentTests) do
    local component = GSF.ComponentRegistry.createComponent(test.type, {
      id = `test_{test.type}`,
      name = `Test {test.type}`,
      customProperties = test.config,
    })

    if component then
      print(`  ✅ {test.type} component created successfully`)
    else
      warn(`  ❌ Failed to create {test.type} component`)
    end
  end
end

function IntegrationTest.quickEffectsTest(): ()
  print("\n✨ Quick Effects Test")

  -- Test effects system directly
  if GSF.EffectsSystem then
    print("  ✅ Effects system available")
    -- Could add direct effect tests here
  else
    warn("  ❌ Effects system not available")
  end

  -- Test audio system directly
  if GSF.AudioSystem then
    print("  ✅ Audio system available")
    -- Could add direct audio tests here
  else
    warn("  ❌ Audio system not available")
  end
end

return IntegrationTest
