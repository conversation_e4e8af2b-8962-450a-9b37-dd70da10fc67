--!strict
--[[
	Gun System Framework - System Integrity Test
	
	This module performs comprehensive testing to ensure all systems are properly
	connected and functioning correctly:
	- Module loading and initialization
	- Component factory registration
	- System integration verification
	- Performance system validation
	- UI system connectivity
	- Error handling and edge cases
]]

local GSF = require(script.Parent.Parent.Core)

local SystemIntegrityTest = {}

-- Test results tracking
local testResults = {
	passed = 0,
	failed = 0,
	warnings = 0,
	errors = {},
}

-- ============================================================================
-- CORE SYSTEM TESTS
-- ============================================================================

function SystemIntegrityTest.runFullSystemIntegrityTest(): ()
	print("\n🔍 === SYSTEM INTEGRITY TEST ===")
	print("Verifying all GSF systems are properly connected...")
	
	-- Reset test results
	testResults = {passed = 0, failed = 0, warnings = 0, errors = {}}
	
	-- Test 1: Core module loading
	print("\n--- Test 1: Core Module Loading ---")
	SystemIntegrityTest.testCoreModuleLoading()
	
	-- Test 2: Component system integration
	print("\n--- Test 2: Component System Integration ---")
	SystemIntegrityTest.testComponentSystemIntegration()
	
	-- Test 3: Event system connectivity
	print("\n--- Test 3: Event System Connectivity ---")
	SystemIntegrityTest.testEventSystemConnectivity()
	
	-- Test 4: Performance systems
	print("\n--- Test 4: Performance Systems ---")
	SystemIntegrityTest.testPerformanceSystems()
	
	-- Test 5: UI system integration
	print("\n--- Test 5: UI System Integration ---")
	SystemIntegrityTest.testUISystemIntegration()
	
	-- Test 6: Advanced systems
	print("\n--- Test 6: Advanced Systems ---")
	SystemIntegrityTest.testAdvancedSystems()
	
	-- Test 7: Error handling
	print("\n--- Test 7: Error Handling ---")
	SystemIntegrityTest.testErrorHandling()
	
	-- Generate final report
	print("\n--- Final Integrity Report ---")
	SystemIntegrityTest.generateIntegrityReport()
	
	print("\n✅ === SYSTEM INTEGRITY TEST COMPLETE ===")
end

function SystemIntegrityTest.testCoreModuleLoading(): ()
	print("Testing core module loading...")
	
	-- Test GSF main module
	if GSF then
		SystemIntegrityTest.pass("GSF main module loaded")
	else
		SystemIntegrityTest.fail("GSF main module failed to load")
		return
	end
	
	-- Test core systems
	local coreSystems = {
		"WeaponEntity",
		"ComponentRegistry", 
		"EventBus",
		"ProjectileSystem",
		"EffectsSystem",
		"AudioSystem",
		"AdvancedBallistics",
		"EnvironmentalSystem",
		"PerformanceMonitor",
		"LODSystem",
		"ObjectPool",
		"WeaponCustomizationUI"
	}
	
	for _, systemName in ipairs(coreSystems) do
		if GSF[systemName] then
			SystemIntegrityTest.pass(`{systemName} system loaded`)
		else
			SystemIntegrityTest.fail(`{systemName} system missing`)
		end
	end
	
	-- Test initialization
	if GSF.isInitialized then
		SystemIntegrityTest.pass("GSF initialization status available")
	else
		SystemIntegrityTest.warn("GSF initialization status unknown")
	end
end

function SystemIntegrityTest.testComponentSystemIntegration(): ()
	print("Testing component system integration...")
	
	-- Test component factories
	local componentFactories = {
		"createBarrel",
		"createMagazine", 
		"createSight",
		"createStock",
		"createGrip",
		"createSuppressor"
	}
	
	for _, factoryName in ipairs(componentFactories) do
		if GSF[factoryName] then
			SystemIntegrityTest.pass(`{factoryName} factory available`)
			
			-- Test factory functionality
			local success, result = pcall(function()
				return GSF[factoryName]({
					id = `test_{factoryName}`,
					name = `Test {factoryName}`,
				})
			end)
			
			if success and result then
				SystemIntegrityTest.pass(`{factoryName} factory functional`)
			else
				SystemIntegrityTest.fail(`{factoryName} factory non-functional: {result}`)
			end
		else
			SystemIntegrityTest.fail(`{factoryName} factory missing`)
		end
	end
	
	-- Test component registry
	if GSF.ComponentRegistry then
		local registeredFactories = GSF.ComponentRegistry.getRegisteredFactories()
		if registeredFactories and next(registeredFactories) then
			SystemIntegrityTest.pass(`Component registry has {#registeredFactories} factories`)
		else
			SystemIntegrityTest.warn("Component registry appears empty")
		end
	else
		SystemIntegrityTest.fail("Component registry not accessible")
	end
end

function SystemIntegrityTest.testEventSystemConnectivity(): ()
	print("Testing event system connectivity...")
	
	if not GSF.EventBus then
		SystemIntegrityTest.fail("EventBus not available")
		return
	end
	
	-- Test event subscription
	local testEventReceived = false
	local subscriptionId = GSF.EventBus.subscribe("test_event", function(event)
		testEventReceived = true
	end)
	
	if subscriptionId then
		SystemIntegrityTest.pass("Event subscription successful")
		
		-- Test event publishing
		GSF.EventBus.publish("test_event", {test = true})
		
		-- Wait a moment for event processing
		task.wait(0.1)
		
		if testEventReceived then
			SystemIntegrityTest.pass("Event publishing and receiving functional")
		else
			SystemIntegrityTest.fail("Event not received")
		end
		
		-- Test unsubscription
		local unsubResult = GSF.EventBus.unsubscribe(subscriptionId)
		if unsubResult then
			SystemIntegrityTest.pass("Event unsubscription successful")
		else
			SystemIntegrityTest.warn("Event unsubscription failed")
		end
	else
		SystemIntegrityTest.fail("Event subscription failed")
	end
end

function SystemIntegrityTest.testPerformanceSystems(): ()
	print("Testing performance systems...")
	
	-- Test Performance Monitor
	if GSF.PerformanceMonitor then
		local report = GSF.PerformanceMonitor.getPerformanceReport()
		if report and report.frameRate then
			SystemIntegrityTest.pass("Performance Monitor functional")
		else
			SystemIntegrityTest.fail("Performance Monitor not generating reports")
		end
	else
		SystemIntegrityTest.fail("Performance Monitor not available")
	end
	
	-- Test LOD System
	if GSF.LODSystem then
		local lodStats = GSF.LODSystem.getLODStatistics()
		if lodStats then
			SystemIntegrityTest.pass("LOD System functional")
		else
			SystemIntegrityTest.fail("LOD System not generating statistics")
		end
	else
		SystemIntegrityTest.fail("LOD System not available")
	end
	
	-- Test Object Pool
	if GSF.ObjectPool then
		local poolCount = GSF.ObjectPool.getActivePoolCount()
		if poolCount >= 0 then
			SystemIntegrityTest.pass(`Object Pool functional ({poolCount} pools)`)
		else
			SystemIntegrityTest.fail("Object Pool not functional")
		end
	else
		SystemIntegrityTest.fail("Object Pool not available")
	end
end

function SystemIntegrityTest.testUISystemIntegration(): ()
	print("Testing UI system integration...")
	
	if not game:GetService("RunService"):IsClient() then
		SystemIntegrityTest.warn("UI tests skipped - not running on client")
		return
	end
	
	if GSF.WeaponCustomizationUI then
		-- Test UI state checking
		local isOpen = GSF.WeaponCustomizationUI.isUIOpen()
		if isOpen ~= nil then
			SystemIntegrityTest.pass("UI state checking functional")
		else
			SystemIntegrityTest.fail("UI state checking failed")
		end
	else
		SystemIntegrityTest.fail("Weapon Customization UI not available")
	end
end

function SystemIntegrityTest.testAdvancedSystems(): ()
	print("Testing advanced systems...")
	
	-- Test Advanced Ballistics
	if GSF.AdvancedBallistics then
		local testProjectile = {
			position = {X = 0, Y = 0, Z = 0},
			velocity = {X = 0, Y = 0, Z = -800},
			mass = 0.004,
			caliber = 5.56,
		}
		
		local success, result = pcall(function()
			return GSF.AdvancedBallistics.calculateTrajectory(testProjectile, 1.0)
		end)
		
		if success then
			SystemIntegrityTest.pass("Advanced Ballistics functional")
		else
			SystemIntegrityTest.fail(`Advanced Ballistics error: {result}`)
		end
	else
		SystemIntegrityTest.fail("Advanced Ballistics not available")
	end
	
	-- Test Environmental System
	if GSF.EnvironmentalSystem then
		local conditions = GSF.EnvironmentalSystem.getCurrentConditions()
		if conditions then
			SystemIntegrityTest.pass("Environmental System functional")
		else
			SystemIntegrityTest.fail("Environmental System not providing conditions")
		end
	else
		SystemIntegrityTest.fail("Environmental System not available")
	end
end

function SystemIntegrityTest.testErrorHandling(): ()
	print("Testing error handling...")
	
	-- Test invalid weapon creation
	local success, result = pcall(function()
		return GSF.WeaponEntity.new(nil)
	end)
	
	if not success then
		SystemIntegrityTest.pass("Invalid weapon creation properly rejected")
	else
		SystemIntegrityTest.warn("Invalid weapon creation should be rejected")
	end
	
	-- Test invalid component attachment
	if GSF.createBarrel then
		local testBarrel = GSF.createBarrel({
			id = "test_error_barrel",
			name = "Test Barrel",
		})
		
		-- Try to attach to non-existent weapon
		local success2, result2 = pcall(function()
			return testBarrel:attach(nil, "barrel_mount")
		end)
		
		if not success2 then
			SystemIntegrityTest.pass("Invalid component attachment properly rejected")
		else
			SystemIntegrityTest.warn("Invalid component attachment should be rejected")
		end
	end
end

-- ============================================================================
-- TEST RESULT TRACKING
-- ============================================================================

function SystemIntegrityTest.pass(message: string): ()
	print(`  ✅ {message}`)
	testResults.passed += 1
end

function SystemIntegrityTest.fail(message: string): ()
	print(`  ❌ {message}`)
	testResults.failed += 1
	table.insert(testResults.errors, message)
end

function SystemIntegrityTest.warn(message: string): ()
	print(`  ⚠️ {message}`)
	testResults.warnings += 1
end

function SystemIntegrityTest.generateIntegrityReport(): ()
	local total = testResults.passed + testResults.failed + testResults.warnings
	
	print("\n📊 SYSTEM INTEGRITY REPORT:")
	print(`  Total Tests: {total}`)
	print(`  Passed: {testResults.passed} ({math.floor(testResults.passed/total*100)}%)`)
	print(`  Failed: {testResults.failed} ({math.floor(testResults.failed/total*100)}%)`)
	print(`  Warnings: {testResults.warnings} ({math.floor(testResults.warnings/total*100)}%)`)
	
	-- Overall assessment
	if testResults.failed == 0 then
		if testResults.warnings == 0 then
			print("\n🟢 SYSTEM STATUS: EXCELLENT")
			print("All systems are properly connected and functional.")
		else
			print("\n🟡 SYSTEM STATUS: GOOD")
			print("All critical systems functional with minor warnings.")
		end
	elseif testResults.failed <= 2 then
		print("\n🟠 SYSTEM STATUS: ACCEPTABLE")
		print("Most systems functional but some issues need attention.")
	else
		print("\n🔴 SYSTEM STATUS: CRITICAL")
		print("Multiple system failures detected - requires immediate attention.")
	end
	
	-- List critical errors
	if #testResults.errors > 0 then
		print("\n❌ CRITICAL ERRORS:")
		for _, error in ipairs(testResults.errors) do
			print(`  • {error}`)
		end
	end
	
	-- Recommendations
	print("\n💡 RECOMMENDATIONS:")
	if testResults.failed > 0 then
		print("  • Fix critical errors before deployment")
		print("  • Run individual system tests for detailed diagnostics")
	end
	if testResults.warnings > 0 then
		print("  • Review warnings for potential improvements")
	end
	print("  • Run performance tests under load")
	print("  • Validate with real weapon configurations")
end

-- ============================================================================
-- QUICK INTEGRITY CHECK
-- ============================================================================

function SystemIntegrityTest.quickIntegrityCheck(): ()
	print("\n⚡ Quick System Integrity Check")
	
	local criticalSystems = {
		"WeaponEntity",
		"ComponentRegistry",
		"EventBus",
		"ProjectileSystem"
	}
	
	local allCriticalSystemsOK = true
	
	for _, systemName in ipairs(criticalSystems) do
		if GSF[systemName] then
			print(`  ✅ {systemName}`)
		else
			print(`  ❌ {systemName} MISSING`)
			allCriticalSystemsOK = false
		end
	end
	
	if allCriticalSystemsOK then
		print("  🟢 All critical systems operational")
	else
		print("  🔴 Critical system failures detected")
	end
	
	-- Quick component test
	local success, _ = pcall(function()
		return GSF.createBarrel({id = "quick_test", name = "Quick Test"})
	end)
	
	if success then
		print("  ✅ Component creation functional")
	else
		print("  ❌ Component creation failed")
	end
end

return SystemIntegrityTest
