--!strict
--[[
	Gun System Framework - Visual Effects System
	
	This module manages all visual effects for the weapon system including:
	- Muzzle flash effects
	- Shell ejection
	- Impact effects
	- Particle systems
	- Environmental interactions
]]

local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")
local Debris = game:GetService("Debris")

local Types = require(script.Parent.Parent.Types)

-- Type imports
type Weapon = Types.Weapon
type Vector3 = Types.Vector3
type MuzzleFlashData = Types.MuzzleFlashData
type ShellEjectionData = Types.ShellEjectionData
type ImpactEffectData = Types.ImpactEffectData
type ParticleEffectData = Types.ParticleEffectData

-- ============================================================================
-- EFFECTS SYSTEM IMPLEMENTATION
-- ============================================================================

local EffectsSystem = {}
EffectsSystem.__index = EffectsSystem

-- Effect pools for performance optimization
local effectPools = {
	muzzleFlash = {},
	shellCasing = {},
	impactSpark = {},
	smokeParticle = {},
}

-- Active effects tracking
local activeEffects = {}
local effectCounter = 0

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

function EffectsSystem.initialize(): ()
	print("[EffectsSystem] Initializing visual effects system...")
	
	-- Pre-populate effect pools
	EffectsSystem._createEffectPools()
	
	-- Start update loop
	RunService.Heartbeat:Connect(EffectsSystem._updateEffects)
	
	print("[EffectsSystem] Visual effects system initialized")
end

function EffectsSystem._createEffectPools(): ()
	-- Create muzzle flash pool
	for i = 1, 20 do
		local flash = EffectsSystem._createMuzzleFlashEffect()
		flash.Parent = nil
		table.insert(effectPools.muzzleFlash, flash)
	end
	
	-- Create shell casing pool
	for i = 1, 50 do
		local casing = EffectsSystem._createShellCasingEffect()
		casing.Parent = nil
		table.insert(effectPools.shellCasing, casing)
	end
	
	-- Create impact spark pool
	for i = 1, 30 do
		local spark = EffectsSystem._createImpactSparkEffect()
		spark.Parent = nil
		table.insert(effectPools.impactSpark, spark)
	end
	
	print("[EffectsSystem] Created effect pools")
end

-- ============================================================================
-- MUZZLE FLASH EFFECTS
-- ============================================================================

function EffectsSystem.createMuzzleFlash(weapon: Weapon, fireData: any): ()
	local muzzlePosition = EffectsSystem._getMuzzlePosition(weapon)
	local muzzleDirection = EffectsSystem._getMuzzleDirection(weapon)
	
	-- Get muzzle flash configuration
	local flashData = EffectsSystem._calculateMuzzleFlashData(weapon, fireData)
	
	-- Apply suppressor modifications
	local suppressor = weapon:getComponent("Suppressor")
	if suppressor then
		flashData = suppressor:modifyMuzzleFlash(flashData)
	end
	
	-- Create flash effect
	local flashEffect = EffectsSystem._getPooledEffect("muzzleFlash")
	if flashEffect then
		EffectsSystem._configureMuzzleFlash(flashEffect, muzzlePosition, muzzleDirection, flashData)
		EffectsSystem._playMuzzleFlash(flashEffect, flashData.duration)
	end
	
	-- Create muzzle smoke
	EffectsSystem._createMuzzleSmoke(muzzlePosition, muzzleDirection, flashData)
end

function EffectsSystem._createMuzzleFlashEffect(): Part
	local flash = Instance.new("Part")
	flash.Name = "MuzzleFlash"
	flash.Material = Enum.Material.Neon
	flash.Shape = Enum.PartType.Ball
	flash.Size = Vector3.new(0.5, 0.5, 0.5)
	flash.Anchored = true
	flash.CanCollide = false
	flash.Color = Color3.fromRGB(255, 200, 100)
	
	-- Add point light
	local light = Instance.new("PointLight")
	light.Brightness = 2
	light.Range = 10
	light.Color = Color3.fromRGB(255, 150, 50)
	light.Parent = flash
	
	return flash
end

function EffectsSystem._configureMuzzleFlash(flash: Part, position: Vector3, direction: Vector3, data: MuzzleFlashData): ()
	flash.Position = Vector3.new(position.X, position.Y, position.Z)
	flash.Size = Vector3.new(data.size, data.size * 0.6, data.size * 1.2)
	flash.Color = data.color
	flash.Transparency = 0.3
	
	-- Configure light
	local light = flash:FindFirstChild("PointLight")
	if light then
		light.Brightness = data.brightness
		light.Range = data.lightRange
		light.Color = data.color
	end
	
	-- Orient flash along barrel direction
	local cf = CFrame.lookAt(
		Vector3.new(position.X, position.Y, position.Z),
		Vector3.new(position.X + direction.X, position.Y + direction.Y, position.Z + direction.Z)
	)
	flash.CFrame = cf
end

function EffectsSystem._playMuzzleFlash(flash: Part, duration: number): ()
	flash.Parent = workspace
	effectCounter += 1
	local effectId = effectCounter
	
	activeEffects[effectId] = {
		effect = flash,
		type = "muzzleFlash",
		startTime = tick(),
		duration = duration,
	}
	
	-- Animate flash
	local fadeInfo = TweenInfo.new(duration, Enum.EasingStyle.Quart, Enum.EasingDirection.Out)
	local fadeTween = TweenService:Create(flash, fadeInfo, {
		Transparency = 1,
		Size = flash.Size * 0.1,
	})
	
	local light = flash:FindFirstChild("PointLight")
	if light then
		local lightTween = TweenService:Create(light, fadeInfo, {
			Brightness = 0,
		})
		lightTween:Play()
	end
	
	fadeTween:Play()
end

-- ============================================================================
-- SHELL EJECTION EFFECTS
-- ============================================================================

function EffectsSystem.createShellEjection(weapon: Weapon, fireData: any): ()
	local ejectionPort = EffectsSystem._getEjectionPortPosition(weapon)
	local ejectionDirection = EffectsSystem._getEjectionDirection(weapon)
	
	-- Get shell ejection configuration
	local shellData = EffectsSystem._calculateShellEjectionData(weapon, fireData)
	
	-- Create shell casing
	local shellCasing = EffectsSystem._getPooledEffect("shellCasing")
	if shellCasing then
		EffectsSystem._configureShellCasing(shellCasing, ejectionPort, ejectionDirection, shellData)
		EffectsSystem._animateShellEjection(shellCasing, shellData)
	end
end

function EffectsSystem._createShellCasingEffect(): Part
	local casing = Instance.new("Part")
	casing.Name = "ShellCasing"
	casing.Material = Enum.Material.Metal
	casing.Shape = Enum.PartType.Cylinder
	casing.Size = Vector3.new(0.1, 0.05, 0.05)
	casing.Color = Color3.fromRGB(200, 180, 120)
	casing.CanCollide = true
	
	-- Add physics properties
	local bodyVelocity = Instance.new("BodyVelocity")
	bodyVelocity.MaxForce = Vector3.new(4000, 4000, 4000)
	bodyVelocity.Parent = casing
	
	local bodyAngularVelocity = Instance.new("BodyAngularVelocity")
	bodyAngularVelocity.MaxTorque = Vector3.new(4000, 4000, 4000)
	bodyAngularVelocity.Parent = casing
	
	return casing
end

function EffectsSystem._configureShellCasing(casing: Part, position: Vector3, direction: Vector3, data: ShellEjectionData): ()
	casing.Position = Vector3.new(position.X, position.Y, position.Z)
	casing.Size = Vector3.new(data.length, data.diameter, data.diameter)
	
	-- Set initial velocity
	local bodyVelocity = casing:FindFirstChild("BodyVelocity")
	if bodyVelocity then
		local velocity = Vector3.new(
			direction.X * data.velocity + (math.random() - 0.5) * 2,
			direction.Y * data.velocity + math.random() * 3 + 2,
			direction.Z * data.velocity + (math.random() - 0.5) * 2
		)
		bodyVelocity.Velocity = velocity
	end
	
	-- Set angular velocity for tumbling
	local bodyAngularVelocity = casing:FindFirstChild("BodyAngularVelocity")
	if bodyAngularVelocity then
		bodyAngularVelocity.AngularVelocity = Vector3.new(
			(math.random() - 0.5) * 20,
			(math.random() - 0.5) * 20,
			(math.random() - 0.5) * 20
		)
	end
end

function EffectsSystem._animateShellEjection(casing: Part, data: ShellEjectionData): ()
	casing.Parent = workspace
	effectCounter += 1
	local effectId = effectCounter
	
	activeEffects[effectId] = {
		effect = casing,
		type = "shellCasing",
		startTime = tick(),
		duration = data.lifetime,
	}
	
	-- Clean up after lifetime
	Debris:AddItem(casing, data.lifetime)
end

-- ============================================================================
-- IMPACT EFFECTS
-- ============================================================================

function EffectsSystem.createImpactEffect(position: Vector3, normal: Vector3, material: string, velocity: number): ()
	-- Create impact sparks
	local sparkEffect = EffectsSystem._getPooledEffect("impactSpark")
	if sparkEffect then
		EffectsSystem._configureImpactSpark(sparkEffect, position, normal, material, velocity)
		EffectsSystem._playImpactSpark(sparkEffect)
	end
	
	-- Create impact smoke/dust
	EffectsSystem._createImpactSmoke(position, normal, material)
	
	-- Create material-specific effects
	if material == "Metal" then
		EffectsSystem._createMetalImpactEffect(position, normal, velocity)
	elseif material == "Concrete" then
		EffectsSystem._createConcreteImpactEffect(position, normal, velocity)
	elseif material == "Wood" then
		EffectsSystem._createWoodImpactEffect(position, normal, velocity)
	end
end

function EffectsSystem._createImpactSparkEffect(): Part
	local spark = Instance.new("Part")
	spark.Name = "ImpactSpark"
	spark.Material = Enum.Material.Neon
	spark.Shape = Enum.PartType.Ball
	spark.Size = Vector3.new(0.1, 0.1, 0.1)
	spark.Anchored = true
	spark.CanCollide = false
	spark.Color = Color3.fromRGB(255, 255, 200)
	
	return spark
end

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

function EffectsSystem._getPooledEffect(effectType: string): Part?
	local pool = effectPools[effectType]
	if pool and #pool > 0 then
		return table.remove(pool, #pool)
	end
	
	-- Create new effect if pool is empty
	if effectType == "muzzleFlash" then
		return EffectsSystem._createMuzzleFlashEffect()
	elseif effectType == "shellCasing" then
		return EffectsSystem._createShellCasingEffect()
	elseif effectType == "impactSpark" then
		return EffectsSystem._createImpactSparkEffect()
	end
	
	return nil
end

function EffectsSystem._returnToPool(effect: Part, effectType: string): ()
	effect.Parent = nil
	effect.Transparency = 0
	
	-- Reset effect properties
	if effectType == "muzzleFlash" then
		local light = effect:FindFirstChild("PointLight")
		if light then
			light.Brightness = 2
		end
	end
	
	table.insert(effectPools[effectType], effect)
end

function EffectsSystem._updateEffects(): ()
	local currentTime = tick()
	local toRemove = {}
	
	for effectId, effectData in pairs(activeEffects) do
		if currentTime - effectData.startTime >= effectData.duration then
			-- Return effect to pool
			EffectsSystem._returnToPool(effectData.effect, effectData.type)
			table.insert(toRemove, effectId)
		end
	end
	
	-- Clean up expired effects
	for _, effectId in ipairs(toRemove) do
		activeEffects[effectId] = nil
	end
end

function EffectsSystem._getMuzzlePosition(weapon: Weapon): Vector3
	-- Get muzzle position from barrel component
	local barrel = weapon:getComponent("Barrel")
	if barrel and weapon.instance then
		local weaponCFrame = weapon.instance.CFrame
		local muzzleOffset = Vector3.new(0, 0, -barrel.length * 0.0254) -- Convert inches to studs
		return weaponCFrame:PointToWorldSpace(muzzleOffset)
	end
	
	return {X = 0, Y = 0, Z = 0}
end

function EffectsSystem._getMuzzleDirection(weapon: Weapon): Vector3
	if weapon.instance then
		return weapon.instance.CFrame.LookVector
	end
	
	return {X = 0, Y = 0, Z = -1}
end

function EffectsSystem._calculateMuzzleFlashData(weapon: Weapon, fireData: any): MuzzleFlashData
	local barrel = weapon:getComponent("Barrel")
	local baseSize = 1.0
	
	if barrel then
		-- Longer barrels have smaller flash
		baseSize = math.max(0.3, 1.0 - (barrel.length - 10) * 0.02)
	end
	
	return {
		size = baseSize,
		brightness = 2.0,
		lightRange = 8.0,
		color = Color3.fromRGB(255, 200, 100),
		duration = 0.1,
	}
end

return EffectsSystem
