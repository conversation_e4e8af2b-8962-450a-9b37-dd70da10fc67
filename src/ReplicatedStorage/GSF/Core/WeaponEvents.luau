--!strict
--[[
	Gun System Framework - Weapon Events Helper

	This module provides helper functions for creating and managing
	weapon-related events. It standardizes event creation and provides
	type-safe event builders.
]]

local EventBus = require(script.EventBus)
local Types = require(script.Parent.Parent.Types)

-- Type imports
type GameEvent = Types.GameEvent
type EventType = Types.EventType
type Weapon = Types.Weapon
type WeaponComponent = Types.WeaponComponent
type FireResult = Types.FireResult
type ReloadResult = Types.ReloadResult
type Player = Types.Player
type Vector3 = Types.Vector3

-- ============================================================================
-- EVENT BUILDERS
-- ============================================================================

local WeaponEvents = {}

-- ============================================================================
-- WEAPON OPERATION EVENTS
-- ============================================================================

function WeaponEvents.createWeaponFiredEvent(
  weapon: Weapon,
  fireResult: FireResult,
  player: Player?
): GameEvent
  return {
    type = "WeaponFired",
    timestamp = tick(),
    source = weapon.id,
    data = {
      weaponId = weapon.id,
      weaponName = weapon.name,
      weaponCategory = weapon.category,
      playerId = player and player.UserId or nil,
      playerName = player and player.Name or nil,
      fireResult = fireResult,
      ammunition = weapon.currentStatistics.defaultAmmo,
      fireMode = weapon.state.modes.fireMode,
      origin = fireResult.origin,
      direction = fireResult.direction,
      velocity = fireResult.velocity,
      projectileId = fireResult.projectileId,
    },
    priority = 1,
    replicateToClients = true,
    replicateToServer = false,
    targetPlayers = nil,
  }
end

function WeaponEvents.createWeaponReloadedEvent(
  weapon: Weapon,
  reloadResult: ReloadResult,
  player: Player?
): GameEvent
  return {
    type = "WeaponReloaded",
    timestamp = tick(),
    source = weapon.id,
    data = {
      weaponId = weapon.id,
      weaponName = weapon.name,
      playerId = player and player.UserId or nil,
      playerName = player and player.Name or nil,
      reloadResult = reloadResult,
      reloadType = reloadResult.reloadType,
      duration = reloadResult.duration,
      ammunitionAdded = reloadResult.ammunitionAdded,
      currentAmmo = {
        chambered = weapon.state.ammunition.chambered,
        magazine = weapon.state.ammunition.magazine,
        reserve = weapon.state.ammunition.reserve,
      },
    },
    priority = 2,
    replicateToClients = true,
    replicateToServer = false,
    targetPlayers = nil,
  }
end

function WeaponEvents.createWeaponJammedEvent(
  weapon: Weapon,
  jamCause: string,
  player: Player?
): GameEvent
  return {
    type = "WeaponJammed",
    timestamp = tick(),
    source = weapon.id,
    data = {
      weaponId = weapon.id,
      weaponName = weapon.name,
      playerId = player and player.UserId or nil,
      playerName = player and player.Name or nil,
      jamCause = jamCause,
      weaponCondition = weapon.state.condition,
      jamChance = weapon.state.condition.jamChance,
      durability = weapon.state.condition.durability,
      fouling = weapon.state.condition.fouling,
    },
    priority = 1,
    replicateToClients = true,
    replicateToServer = true,
    targetPlayers = nil,
  }
end

function WeaponEvents.createWeaponSwitchedEvent(
  oldWeapon: Weapon?,
  newWeapon: Weapon,
  player: Player
): GameEvent
  return {
    type = "WeaponSwitched",
    timestamp = tick(),
    source = `player_{player.UserId}`,
    data = {
      playerId = player.UserId,
      playerName = player.Name,
      oldWeaponId = oldWeapon and oldWeapon.id or nil,
      oldWeaponName = oldWeapon and oldWeapon.name or nil,
      newWeaponId = newWeapon.id,
      newWeaponName = newWeapon.name,
      newWeaponCategory = newWeapon.category,
      switchTime = tick(),
    },
    priority = 2,
    replicateToClients = true,
    replicateToServer = true,
    targetPlayers = nil,
  }
end

-- ============================================================================
-- COMPONENT EVENTS
-- ============================================================================

function WeaponEvents.createComponentAttachedEvent(
  weapon: Weapon,
  component: WeaponComponent,
  attachmentPoint: string,
  player: Player?
): GameEvent
  return {
    type = "ComponentAttached",
    timestamp = tick(),
    source = weapon.id,
    data = {
      weaponId = weapon.id,
      weaponName = weapon.name,
      playerId = player and player.UserId or nil,
      playerName = player and player.Name or nil,
      componentId = component.id,
      componentType = component.type,
      componentName = component.name,
      attachmentPoint = attachmentPoint,
      modifiedStats = weapon.currentStatistics,
    },
    priority = 2,
    replicateToClients = true,
    replicateToServer = true,
    targetPlayers = nil,
  }
end

function WeaponEvents.createComponentDetachedEvent(
  weapon: Weapon,
  component: WeaponComponent,
  player: Player?
): GameEvent
  return {
    type = "ComponentDetached",
    timestamp = tick(),
    source = weapon.id,
    data = {
      weaponId = weapon.id,
      weaponName = weapon.name,
      playerId = player and player.UserId or nil,
      playerName = player and player.Name or nil,
      componentId = component.id,
      componentType = component.type,
      componentName = component.name,
      restoredStats = weapon.currentStatistics,
    },
    priority = 2,
    replicateToClients = true,
    replicateToServer = true,
    targetPlayers = nil,
  }
end

-- ============================================================================
-- PROJECTILE AND DAMAGE EVENTS
-- ============================================================================

function WeaponEvents.createProjectileHitEvent(
  projectileId: string,
  hitPosition: Vector3,
  hitTarget: any, -- Could be Player, NPC, or Instance
  damage: number,
  weaponId: string
): GameEvent
  return {
    type = "ProjectileHit",
    timestamp = tick(),
    source = projectileId,
    data = {
      projectileId = projectileId,
      weaponId = weaponId,
      hitPosition = hitPosition,
      hitTarget = hitTarget,
      damage = damage,
      isHeadshot = false, -- Would be calculated by ballistics system
      isKill = false, -- Would be determined by damage system
      penetrated = false, -- Would be determined by ballistics
    },
    priority = 1,
    replicateToClients = true,
    replicateToServer = true,
    targetPlayers = nil,
  }
end

function WeaponEvents.createPlayerDamagedEvent(
  victim: Player,
  attacker: Player?,
  damage: number,
  weaponId: string,
  bodyPart: string
): GameEvent
  return {
    type = "PlayerDamaged",
    timestamp = tick(),
    source = weaponId,
    data = {
      victimId = victim.UserId,
      victimName = victim.Name,
      attackerId = attacker and attacker.UserId or nil,
      attackerName = attacker and attacker.Name or nil,
      damage = damage,
      weaponId = weaponId,
      bodyPart = bodyPart,
      isHeadshot = bodyPart == "Head",
      remainingHealth = 100, -- Would be calculated by health system
    },
    priority = 1,
    replicateToClients = true,
    replicateToServer = true,
    targetPlayers = nil,
  }
end

-- ============================================================================
-- EFFECT AND AUDIO EVENTS
-- ============================================================================

function WeaponEvents.createEffectCreatedEvent(
  effectId: string,
  effectType: string,
  position: Vector3,
  sourceWeaponId: string?
): GameEvent
  return {
    type = "EffectCreated",
    timestamp = tick(),
    source = sourceWeaponId or effectId,
    data = {
      effectId = effectId,
      effectType = effectType,
      position = position,
      sourceWeaponId = sourceWeaponId,
      duration = 1.0, -- Default duration
      intensity = 1.0, -- Default intensity
    },
    priority = 3,
    replicateToClients = true,
    replicateToServer = false,
    targetPlayers = nil,
  }
end

function WeaponEvents.createAudioPlayedEvent(
  audioId: string,
  audioType: string,
  position: Vector3?,
  sourceWeaponId: string?
): GameEvent
  return {
    type = "AudioPlayed",
    timestamp = tick(),
    source = sourceWeaponId or audioId,
    data = {
      audioId = audioId,
      audioType = audioType,
      position = position,
      sourceWeaponId = sourceWeaponId,
      volume = 1.0, -- Default volume
      pitch = 1.0, -- Default pitch
    },
    priority = 3,
    replicateToClients = true,
    replicateToServer = false,
    targetPlayers = nil,
  }
end

-- ============================================================================
-- EVENT PUBLISHING HELPERS
-- ============================================================================

function WeaponEvents.publishWeaponFired(
  weapon: Weapon,
  fireResult: FireResult,
  player: Player?
): ()
  local event = WeaponEvents.createWeaponFiredEvent(weapon, fireResult, player)
  EventBus.publish(event)
end

function WeaponEvents.publishWeaponReloaded(
  weapon: Weapon,
  reloadResult: ReloadResult,
  player: Player?
): ()
  local event = WeaponEvents.createWeaponReloadedEvent(weapon, reloadResult, player)
  EventBus.publish(event)
end

function WeaponEvents.publishWeaponJammed(weapon: Weapon, jamCause: string, player: Player?): ()
  local event = WeaponEvents.createWeaponJammedEvent(weapon, jamCause, player)
  EventBus.publish(event)
end

function WeaponEvents.publishComponentAttached(
  weapon: Weapon,
  component: WeaponComponent,
  attachmentPoint: string,
  player: Player?
): ()
  local event =
    WeaponEvents.createComponentAttachedEvent(weapon, component, attachmentPoint, player)
  EventBus.publish(event)
end

function WeaponEvents.publishComponentDetached(
  weapon: Weapon,
  component: WeaponComponent,
  player: Player?
): ()
  local event = WeaponEvents.createComponentDetachedEvent(weapon, component, player)
  EventBus.publish(event)
end

-- ============================================================================
-- EVENT SUBSCRIPTION HELPERS
-- ============================================================================

function WeaponEvents.subscribeToWeaponEvents(
  weaponId: string,
  handler: (event: GameEvent) -> ()
): string
  -- Create a filter that only passes events for the specific weapon
  local filterId = EventBus.addFilter(function(event: GameEvent): boolean
    return event.data.weaponId == weaponId
  end)

  -- Subscribe to all weapon-related events
  local subscriptionId = EventBus.subscribe("WeaponFired", handler)
  EventBus.subscribe("WeaponReloaded", handler)
  EventBus.subscribe("WeaponJammed", handler)
  EventBus.subscribe("ComponentAttached", handler)
  EventBus.subscribe("ComponentDetached", handler)

  return subscriptionId -- Return the first subscription ID for reference
end

function WeaponEvents.subscribeToPlayerEvents(
  playerId: number,
  handler: (event: GameEvent) -> ()
): string
  -- Create a filter that only passes events for the specific player
  local filterId = EventBus.addFilter(function(event: GameEvent): boolean
    return event.data.playerId == playerId
  end)

  -- Subscribe to all player-related events
  local subscriptionId = EventBus.subscribe("WeaponFired", handler)
  EventBus.subscribe("WeaponReloaded", handler)
  EventBus.subscribe("WeaponSwitched", handler)
  EventBus.subscribe("PlayerDamaged", handler)

  return subscriptionId
end

-- ============================================================================
-- MODULE EXPORT
-- ============================================================================

return WeaponEvents
