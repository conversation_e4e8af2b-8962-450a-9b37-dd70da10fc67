--!strict
--[[
	Gun System Framework - Core Module Initialization

	This module initializes the core GSF systems and registers default component factories.
	It serves as the main entry point for the Gun System Framework.
]]

local AdvancedBallistics = require(script.AdvancedBallistics)
local AnimationSystem = require(script.AnimationSystem)
local AudioSystem = require(script.AudioSystem)
local BarrelFactory = require(script.Parent.Components.BarrelFactory)
local ComponentRegistry = require(script.ComponentRegistry)
local ConfigurationSystem = require(script.ConfigurationSystem)
local EffectsSystem = require(script.EffectsSystem)
local EnvironmentalSystem = require(script.EnvironmentalSystem)
local EventBus = require(script.EventBus)
local GripFactory = require(script.Parent.Components.GripFactory)
local LODSystem = require(script.LODSystem)
local MagazineFactory = require(script.Parent.Components.MagazineFactory)
local ObjectPool = require(script.ObjectPool)
local PerformanceMonitor = require(script.PerformanceMonitor)
local ProjectileSystem = require(script.ProjectileSystem)
local SightFactory = require(script.Parent.Components.SightFactory)
local StockFactory = require(script.Parent.Components.StockFactory)
local SuppressorFactory = require(script.Parent.Components.SuppressorFactory)
local WeaponCustomizationUI = require(script.Parent.UI.WeaponCustomizationUI)
local WeaponEntity = require(script.WeaponEntity)
local WeaponEvents = require(script.WeaponEvents)

-- ============================================================================
-- CORE GSF MODULE
-- ============================================================================

local GSF = {
  -- Version information
  VERSION = "1.0.0",
  API_VERSION = "1.0",

  -- Core systems
  AdvancedBallistics = AdvancedBallistics,
  AnimationSystem = AnimationSystem,
  AudioSystem = AudioSystem,
  ComponentRegistry = ComponentRegistry,
  ConfigurationSystem = ConfigurationSystem,
  EffectsSystem = EffectsSystem,
  EnvironmentalSystem = EnvironmentalSystem,
  EventBus = EventBus,
  LODSystem = LODSystem,
  ObjectPool = ObjectPool,
  PerformanceMonitor = PerformanceMonitor,
  ProjectileSystem = ProjectileSystem,
  WeaponCustomizationUI = WeaponCustomizationUI,
  WeaponEvents = WeaponEvents,
  WeaponEntity = WeaponEntity,

  -- Initialization state
  isInitialized = false,
}

-- ============================================================================
-- INITIALIZATION FUNCTIONS
-- ============================================================================

function GSF.initialize(): boolean
  if GSF.isInitialized then
    warn("[GSF] Already initialized")
    return true
  end

  print("[GSF] Initializing Gun System Framework v" .. GSF.VERSION)

  -- Initialize event bus
  EventBus.initialize()

  -- Initialize projectile system
  ProjectileSystem.initialize()

  -- Initialize configuration system first
  ConfigurationSystem.initialize()

  -- Initialize effects and audio systems
  EffectsSystem.initialize()
  AudioSystem.initialize()
  EnvironmentalSystem.initialize()

  -- Initialize animation system
  AnimationSystem.initialize()

  -- Initialize performance systems
  PerformanceMonitor.initialize()
  ObjectPool.initialize()
  LODSystem.initialize()

  -- Create standard object pools
  ObjectPool.createStandardPools()

  -- Initialize UI systems (client-side only)
  if game:GetService("RunService"):IsClient() then
    WeaponCustomizationUI.initialize()
  end

  -- Register default component factories
  local success = GSF.registerDefaultFactories()
  if not success then
    error("[GSF] Failed to register default component factories")
    return false
  end

  GSF.isInitialized = true
  print("[GSF] Gun System Framework initialized successfully")
  return true
end

function GSF.registerDefaultFactories(): boolean
  print("[GSF] Registering default component factories...")

  local factories = {
    { type = "Barrel", factory = BarrelFactory },
    { type = "Magazine", factory = MagazineFactory },
    { type = "Sight", factory = SightFactory },
    { type = "Stock", factory = StockFactory },
    { type = "Grip", factory = GripFactory },
    { type = "Suppressor", factory = SuppressorFactory },
  }

  for _, factoryInfo in ipairs(factories) do
    local success, errorMessage = pcall(function()
      ComponentRegistry.registerFactory(factoryInfo.type, factoryInfo.factory)
    end)

    if not success then
      error(`[GSF] Failed to register {factoryInfo.type} factory: {errorMessage}`)
      return false
    end
  end

  print(`[GSF] Registered {#factories} default component factories`)
  return true
end

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

function GSF.getSystemInfo(): { [string]: any }
  return {
    version = GSF.VERSION,
    apiVersion = GSF.API_VERSION,
    isInitialized = GSF.isInitialized,
    registeredFactories = ComponentRegistry.getRegisteredFactories(),
    componentStats = ComponentRegistry.getStats(),
    memoryUsage = collectgarbage("count"),
  }
end

function GSF.cleanup(): ()
  print("[GSF] Cleaning up Gun System Framework...")

  -- Cleanup event bus
  EventBus.cleanup()

  -- Cleanup component registry
  ComponentRegistry.cleanup()

  GSF.isInitialized = false
  print("[GSF] Gun System Framework cleanup completed")
end

-- ============================================================================
-- COMPONENT CREATION HELPERS
-- ============================================================================

function GSF.createBarrel(config: { [string]: any }): any
  -- Helper function to create a barrel with sensible defaults
  local barrelConfig = {
    id = config.id or `barrel_{tick()}`,
    name = config.name or "Standard Barrel",
    description = config.description,
    length = config.length or 16,
    mass = config.mass or 1.0,
    customProperties = {
      bore = config.bore or 5.56,
      rifling = config.rifling or "Standard",
      twist = config.twist or 1.0,
      threadPitch = config.threadPitch,
      gasSystem = config.gasSystem or "DI",
    },
  }

  return ComponentRegistry.createComponent("Barrel", barrelConfig)
end

function GSF.createMagazine(config: { [string]: any }): any
  -- Helper function to create a magazine with sensible defaults
  local magazineConfig = {
    id = config.id or `magazine_{tick()}`,
    name = config.name or "Standard Magazine",
    description = config.description,
    mass = config.mass or 0.5,
    customProperties = {
      capacity = config.capacity or 30,
      feedType = config.feedType or "Magazine",
      compatibleAmmo = config.compatibleAmmo or { "556x45" },
      feedReliability = config.feedReliability or 0.98,
    },
  }

  return ComponentRegistry.createComponent("Magazine", magazineConfig)
end

function GSF.createSight(config: { [string]: any }): any
  -- Helper function to create a sight with sensible defaults
  local sightConfig = {
    id = config.id or `sight_{tick()}`,
    name = config.name or "Red Dot Sight",
    description = config.description,
    mass = config.mass or 0.3,
    customProperties = {
      magnification = config.magnification or 1.0,
      reticleType = config.reticleType or "Dot",
      fieldOfView = config.fieldOfView,
      zeroRange = config.zeroRange or 100,
      batteryLevel = config.batteryLevel or 1.0,
      illuminationLevel = config.illuminationLevel or 0.5,
    },
  }

  return ComponentRegistry.createComponent("Sight", sightConfig)
end

function GSF.createStock(config: { [string]: any }): any
  -- Helper function to create a stock with sensible defaults
  local stockConfig = {
    id = config.id or `stock_{tick()}`,
    name = config.name or "Standard Stock",
    description = config.description,
    mass = config.mass or 0.8,
    customProperties = {
      stockType = config.stockType or "Fixed",
      lengthOfPull = config.lengthOfPull or 350, -- mm
      recoilPadType = config.recoilPadType or "Rubber",
      recoilReduction = config.recoilReduction or 0.15,
      isAdjustable = config.isAdjustable or false,
    },
  }

  return ComponentRegistry.createComponent("Stock", stockConfig)
end

function GSF.createSuppressor(config: { [string]: any }): any
  -- Helper function to create a suppressor with sensible defaults
  local suppressorConfig = {
    id = config.id or `suppressor_{tick()}`,
    name = config.name or "Sound Suppressor",
    description = config.description,
    mass = config.mass or 0.6,
    customProperties = {
      suppressorType = config.suppressorType or "Baffle",
      dbReduction = config.dbReduction or 30,
      backPressure = config.backPressure or 1.2,
      boreSize = config.boreSize or 6.0,
      baffleCount = config.baffleCount or 8,
      velocityChange = config.velocityChange or -15,
    },
  }

  return ComponentRegistry.createComponent("Suppressor", suppressorConfig)
end

function GSF.createGrip(config: { [string]: any }): any
  -- Helper function to create a grip with sensible defaults
  local gripConfig = {
    id = config.id or `grip_{tick()}`,
    name = config.name or "Vertical Grip",
    description = config.description,
    mass = config.mass or 0.15,
    customProperties = {
      gripType = config.gripType or "Vertical",
      gripAngle = config.gripAngle or 90,
      textureType = config.textureType or "Stippled",
      palmSwell = config.palmSwell or false,
      fingerGrooves = config.fingerGrooves or false,
      thumbRest = config.thumbRest or false,
    },
  }

  return ComponentRegistry.createComponent("Grip", gripConfig)
end

-- ============================================================================
-- EXAMPLE CONFIGURATIONS
-- ============================================================================

function GSF.getExampleConfigurations(): { [string]: any }
  return {
    -- Example barrel configurations
    barrels = {
      standard_16 = {
        id = "barrel_standard_16",
        name = '16" Standard Barrel',
        description = "Standard 16-inch barrel for assault rifles",
        length = 16,
        bore = 5.56,
        rifling = "Standard",
        twist = 1.0,
        gasSystem = "DI",
      },

      sniper_24 = {
        id = "barrel_sniper_24",
        name = '24" Precision Barrel',
        description = "Long precision barrel for sniper rifles",
        length = 24,
        bore = 7.62,
        rifling = "Polygonal",
        twist = 1.2,
        gasSystem = "None",
      },

      cqb_10 = {
        id = "barrel_cqb_10",
        name = '10" CQB Barrel',
        description = "Short barrel for close quarters combat",
        length = 10,
        bore = 5.56,
        rifling = "Standard",
        twist = 1.0,
        gasSystem = "Piston",
      },
    },

    -- Example magazine configurations
    magazines = {
      standard_30 = {
        id = "magazine_standard_30",
        name = "30-Round Magazine",
        description = "Standard 30-round magazine",
        capacity = 30,
        compatibleAmmo = { "556x45" },
        feedReliability = 0.98,
      },

      extended_60 = {
        id = "magazine_extended_60",
        name = "60-Round Extended Magazine",
        description = "High-capacity extended magazine",
        capacity = 60,
        compatibleAmmo = { "556x45" },
        feedReliability = 0.95,
      },

      pistol_15 = {
        id = "magazine_pistol_15",
        name = "15-Round Pistol Magazine",
        description = "Standard pistol magazine",
        capacity = 15,
        compatibleAmmo = { "9x19" },
        feedReliability = 0.99,
      },
    },

    -- Example sight configurations
    sights = {
      red_dot = {
        id = "sight_red_dot",
        name = "Red Dot Sight",
        description = "1x red dot sight for close to medium range",
        magnification = 1.0,
        reticleType = "Dot",
        batteryLevel = 1.0,
        illuminationLevel = 0.5,
      },

      acog_4x = {
        id = "sight_acog_4x",
        name = "ACOG 4x Scope",
        description = "4x magnified scope for medium to long range",
        magnification = 4.0,
        reticleType = "ACOG",
        fieldOfView = 15,
        zeroRange = 200,
      },
    },

    -- Example suppressor configurations
    suppressors = {
      rifle_suppressor = {
        id = "suppressor_rifle",
        name = "Rifle Suppressor",
        description = "Sound suppressor for assault rifles",
        suppressorType = "Baffle",
        dbReduction = 32,
        boreSize = 6.0,
        baffleCount = 8,
        velocityChange = -12,
      },
    },

    -- Example grip configurations
    grips = {
      vertical_grip = {
        id = "grip_vertical",
        name = "Vertical Foregrip",
        description = "Vertical grip for maximum recoil control",
        gripType = "Vertical",
        gripAngle = 90,
        textureType = "Stippled",
        palmSwell = true,
      },
    },
  }
end

-- ============================================================================
-- MODULE EXPORT
-- ============================================================================

return GSF
