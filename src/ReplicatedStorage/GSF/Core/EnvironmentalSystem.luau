--!strict
--[[
	Gun System Framework - Environmental System
	
	This module manages environmental conditions that affect ballistics:
	- Weather conditions (temperature, humidity, pressure)
	- Wind patterns and gusts
	- Altitude and atmospheric density
	- Time of day and visibility
	- Dynamic weather changes
]]

local RunService = game:GetService("RunService")
local Lighting = game:GetService("Lighting")

local Types = require(script.Parent.Parent.Types)

-- Type imports
type EnvironmentalConditions = Types.EnvironmentalConditions
type Vector3 = Types.Vector3

-- ============================================================================
-- ENVIRONMENTAL SYSTEM
-- ============================================================================

local EnvironmentalSystem = {}
EnvironmentalSystem.__index = EnvironmentalSystem

-- Current environmental state
local currentConditions: EnvironmentalConditions = {
	temperature = 20, -- °C
	pressure = 101325, -- Pa
	humidity = 0.5, -- 0-1
	altitude = 0, -- meters above sea level
	wind = {X = 0, Y = 0, Z = 2}, -- m/s
	visibility = 1.0, -- 0-1
	timeOfDay = 12, -- 24-hour format
	weatherType = "Clear",
}

-- Weather patterns
local weatherPatterns = {
	Clear = {
		temperatureRange = {15, 25},
		humidityRange = {0.3, 0.6},
		windSpeedRange = {0, 5},
		visibilityRange = {0.9, 1.0},
	},
	Cloudy = {
		temperatureRange = {10, 20},
		humidityRange = {0.5, 0.8},
		windSpeedRange = {2, 8},
		visibilityRange = {0.7, 0.9},
	},
	Rainy = {
		temperatureRange = {5, 15},
		humidityRange = {0.8, 1.0},
		windSpeedRange = {5, 15},
		visibilityRange = {0.3, 0.7},
	},
	Stormy = {
		temperatureRange = {0, 10},
		humidityRange = {0.9, 1.0},
		windSpeedRange = {10, 25},
		visibilityRange = {0.1, 0.5},
	},
	Foggy = {
		temperatureRange = {5, 15},
		humidityRange = {0.9, 1.0},
		windSpeedRange = {0, 3},
		visibilityRange = {0.1, 0.3},
	},
}

-- Wind simulation
local windSystem = {
	baseDirection = 0, -- degrees
	baseSpeed = 2, -- m/s
	gustFactor = 0.2,
	gustFrequency = 0.1, -- Hz
	turbulence = 0.1,
	lastGustTime = 0,
}

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

function EnvironmentalSystem.initialize(): ()
	print("[EnvironmentalSystem] Initializing environmental conditions system...")
	
	-- Sync with Roblox lighting
	EnvironmentalSystem._syncWithLighting()
	
	-- Start environmental update loop
	RunService.Heartbeat:Connect(EnvironmentalSystem._updateEnvironment)
	
	print("[EnvironmentalSystem] Environmental system initialized")
end

-- ============================================================================
-- ENVIRONMENTAL CONDITIONS
-- ============================================================================

function EnvironmentalSystem.getCurrentConditions(): EnvironmentalConditions
	return currentConditions
end

function EnvironmentalSystem.setWeather(weatherType: string): ()
	if not weatherPatterns[weatherType] then
		warn(`[EnvironmentalSystem] Unknown weather type: {weatherType}`)
		return
	end
	
	currentConditions.weatherType = weatherType
	local pattern = weatherPatterns[weatherType]
	
	-- Apply weather pattern
	currentConditions.temperature = EnvironmentalSystem._randomInRange(pattern.temperatureRange)
	currentConditions.humidity = EnvironmentalSystem._randomInRange(pattern.humidityRange)
	currentConditions.visibility = EnvironmentalSystem._randomInRange(pattern.visibilityRange)
	
	-- Update wind
	local windSpeed = EnvironmentalSystem._randomInRange(pattern.windSpeedRange)
	windSystem.baseSpeed = windSpeed
	
	print(`[EnvironmentalSystem] Weather changed to {weatherType}`)
end

function EnvironmentalSystem.setWind(direction: number, speed: number, gustFactor: number?): ()
	windSystem.baseDirection = direction
	windSystem.baseSpeed = speed
	windSystem.gustFactor = gustFactor or windSystem.gustFactor
	
	print(`[EnvironmentalSystem] Wind set to {speed}m/s at {direction}°`)
end

function EnvironmentalSystem.setAltitude(altitude: number): ()
	currentConditions.altitude = altitude
	
	-- Adjust pressure and temperature for altitude
	currentConditions.pressure = 101325 * math.exp(-altitude / 8400) -- Barometric formula
	currentConditions.temperature = currentConditions.temperature - (altitude / 1000) * 6.5 -- Lapse rate
	
	print(`[EnvironmentalSystem] Altitude set to {altitude}m`)
end

-- ============================================================================
-- WIND SIMULATION
-- ============================================================================

function EnvironmentalSystem.calculateWindAtPosition(position: Vector3): Vector3
	local currentTime = tick()
	
	-- Base wind vector
	local windDirection = math.rad(windSystem.baseDirection)
	local baseWind = {
		X = math.cos(windDirection) * windSystem.baseSpeed,
		Y = 0,
		Z = math.sin(windDirection) * windSystem.baseSpeed,
	}
	
	-- Add gusts
	local gustWind = EnvironmentalSystem._calculateGustEffect(currentTime, position)
	
	-- Add turbulence
	local turbulenceWind = EnvironmentalSystem._calculateTurbulence(position)
	
	-- Combine wind effects
	local totalWind = {
		X = baseWind.X + gustWind.X + turbulenceWind.X,
		Y = baseWind.Y + gustWind.Y + turbulenceWind.Y,
		Z = baseWind.Z + gustWind.Z + turbulenceWind.Z,
	}
	
	-- Apply altitude effects
	local altitudeFactor = 1 + (position.Y / 1000) * 0.2 -- Wind increases with altitude
	totalWind.X *= altitudeFactor
	totalWind.Z *= altitudeFactor
	
	return totalWind
end

function EnvironmentalSystem._calculateGustEffect(currentTime: number, position: Vector3): Vector3
	-- Check if it's time for a new gust
	if currentTime - windSystem.lastGustTime > (1 / windSystem.gustFrequency) then
		windSystem.lastGustTime = currentTime
	end
	
	-- Calculate gust strength based on time since last gust
	local timeSinceGust = currentTime - windSystem.lastGustTime
	local gustStrength = math.sin(timeSinceGust * math.pi * windSystem.gustFrequency) * windSystem.gustFactor
	
	-- Random gust direction variation
	local gustAngle = windSystem.baseDirection + (math.random() - 0.5) * 60 -- ±30° variation
	local gustDirection = math.rad(gustAngle)
	
	return {
		X = math.cos(gustDirection) * gustStrength * windSystem.baseSpeed,
		Y = (math.random() - 0.5) * gustStrength * windSystem.baseSpeed * 0.5,
		Z = math.sin(gustDirection) * gustStrength * windSystem.baseSpeed,
	}
end

function EnvironmentalSystem._calculateTurbulence(position: Vector3): Vector3
	-- Simple turbulence based on position
	local turbulenceX = math.sin(position.X * 0.01 + tick() * 2) * windSystem.turbulence
	local turbulenceY = math.sin(position.Y * 0.01 + tick() * 1.5) * windSystem.turbulence * 0.5
	local turbulenceZ = math.sin(position.Z * 0.01 + tick() * 2.5) * windSystem.turbulence
	
	return {
		X = turbulenceX * windSystem.baseSpeed,
		Y = turbulenceY * windSystem.baseSpeed,
		Z = turbulenceZ * windSystem.baseSpeed,
	}
end

-- ============================================================================
-- ATMOSPHERIC CALCULATIONS
-- ============================================================================

function EnvironmentalSystem.calculateAirDensity(altitude: number?): number
	local alt = altitude or currentConditions.altitude
	local temp = currentConditions.temperature + 273.15 -- Convert to Kelvin
	local pressure = currentConditions.pressure
	local humidity = currentConditions.humidity
	
	-- Ideal gas law with humidity correction
	local dryAirDensity = pressure / (287.05 * temp)
	local humidityCorrection = 1 - 0.378 * humidity
	
	return dryAirDensity * humidityCorrection
end

function EnvironmentalSystem.calculateSoundSpeed(): number
	local temp = currentConditions.temperature
	return 331.3 * math.sqrt(1 + temp / 273.15)
end

function EnvironmentalSystem.getVisibilityFactor(): number
	return currentConditions.visibility
end

-- ============================================================================
-- BALLISTIC CORRECTIONS
-- ============================================================================

function EnvironmentalSystem.calculateBallisticCorrections(range: number): {[string]: number}
	-- Temperature correction
	local tempCorrection = (currentConditions.temperature - 15) / 10 * 0.01 -- 1% per 10°C
	
	-- Pressure correction
	local pressureCorrection = (currentConditions.pressure - 101325) / 10000 * 0.01 -- 1% per 10kPa
	
	-- Humidity correction
	local humidityCorrection = (currentConditions.humidity - 0.5) * 0.005 -- 0.5% per 0.1 humidity
	
	-- Altitude correction
	local altitudeCorrection = currentConditions.altitude / 1000 * 0.02 -- 2% per 1000m
	
	return {
		temperature = tempCorrection,
		pressure = pressureCorrection,
		humidity = humidityCorrection,
		altitude = altitudeCorrection,
		total = tempCorrection + pressureCorrection + humidityCorrection + altitudeCorrection,
	}
end

-- ============================================================================
-- PRIVATE FUNCTIONS
-- ============================================================================

function EnvironmentalSystem._updateEnvironment(): ()
	-- Update time of day from Lighting
	local clockTime = Lighting.ClockTime
	currentConditions.timeOfDay = clockTime
	
	-- Simulate gradual weather changes
	EnvironmentalSystem._simulateWeatherChanges()
	
	-- Update wind conditions
	currentConditions.wind = EnvironmentalSystem.calculateWindAtPosition({X = 0, Y = 100, Z = 0})
end

function EnvironmentalSystem._syncWithLighting(): ()
	-- Sync environmental conditions with Roblox Lighting
	currentConditions.timeOfDay = Lighting.ClockTime
	
	-- Estimate weather from Lighting properties
	if Lighting.FogEnd < 500 then
		EnvironmentalSystem.setWeather("Foggy")
	elseif Lighting.Brightness < 0.5 then
		EnvironmentalSystem.setWeather("Cloudy")
	else
		EnvironmentalSystem.setWeather("Clear")
	end
end

function EnvironmentalSystem._simulateWeatherChanges(): ()
	-- Gradual temperature changes based on time of day
	local timeOfDay = currentConditions.timeOfDay
	local temperatureVariation = math.sin((timeOfDay - 6) / 12 * math.pi) * 5 -- ±5°C variation
	
	-- Very gradual changes to avoid sudden jumps
	local targetTemp = 20 + temperatureVariation
	currentConditions.temperature = currentConditions.temperature + (targetTemp - currentConditions.temperature) * 0.001
	
	-- Gradual humidity changes
	local targetHumidity = 0.5 + math.sin(timeOfDay / 24 * math.pi * 2) * 0.2
	currentConditions.humidity = currentConditions.humidity + (targetHumidity - currentConditions.humidity) * 0.001
end

function EnvironmentalSystem._randomInRange(range: {number}): number
	return range[1] + math.random() * (range[2] - range[1])
end

-- ============================================================================
-- WEATHER PRESETS
-- ============================================================================

function EnvironmentalSystem.applyWeatherPreset(presetName: string): ()
	local presets = {
		desert = {
			temperature = 35,
			humidity = 0.1,
			pressure = 101325,
			wind = {X = 3, Y = 0, Z = 1},
			visibility = 0.8,
			weatherType = "Clear",
		},
		arctic = {
			temperature = -20,
			humidity = 0.3,
			pressure = 101325,
			wind = {X = 8, Y = 0, Z = 5},
			visibility = 0.6,
			weatherType = "Stormy",
		},
		tropical = {
			temperature = 28,
			humidity = 0.9,
			pressure = 101325,
			wind = {X = 2, Y = 0, Z = 4},
			visibility = 0.7,
			weatherType = "Rainy",
		},
		mountain = {
			temperature = 5,
			humidity = 0.4,
			pressure = 85000, -- High altitude
			wind = {X = 12, Y = 2, Z = 8},
			visibility = 0.9,
			weatherType = "Clear",
		},
	}
	
	local preset = presets[presetName]
	if preset then
		for key, value in pairs(preset) do
			currentConditions[key] = value
		end
		print(`[EnvironmentalSystem] Applied {presetName} weather preset`)
	else
		warn(`[EnvironmentalSystem] Unknown weather preset: {presetName}`)
	end
end

return EnvironmentalSystem
