--!strict
--[[
	Gun System Framework - CFrame-Based Animation System
	
	This module provides a comprehensive CFrame-based animation system for weapons:
	- Procedural recoil animations
	- Smooth weapon sway and movement
	- Component-specific animations (bolt, magazine, etc.)
	- Customizable animation curves and timing
	- Performance-optimized interpolation
]]

local RunService = game:GetService("RunService")
local TweenService = game:GetService("TweenService")

local Types = require(script.Parent.Parent.Types)

-- ============================================================================
-- ANIMATION SYSTEM
-- ============================================================================

local AnimationSystem = {}
AnimationSystem.__index = AnimationSystem

-- Animation state tracking
local activeAnimations = {}
local animationCounter = 0

-- Default animation settings
local defaultSettings = {
	recoil = {
		enabled = true,
		verticalIntensity = 0.1,
		horizontalIntensity = 0.05,
		duration = 0.15,
		recoveryTime = 0.3,
		randomness = 0.2,
	},
	
	sway = {
		enabled = true,
		intensity = 0.02,
		frequency = 1.5,
		breathingEffect = true,
	},
	
	reload = {
		enabled = true,
		magazineDropTime = 0.8,
		magazineInsertTime = 1.5,
		boltReleaseTime = 2.2,
		smoothing = 0.1,
	},
	
	inspection = {
		enabled = true,
		rotationAmount = 45,
		duration = 2.0,
		smoothing = 0.15,
	},
}

-- ============================================================================
-- INITIALIZATION
-- ============================================================================

function AnimationSystem.initialize(): ()
	print("[AnimationSystem] Initializing CFrame-based animation system...")
	
	-- Start animation update loop
	RunService.Heartbeat:Connect(AnimationSystem._updateAnimations)
	
	print("[AnimationSystem] Animation system initialized")
end

-- ============================================================================
-- WEAPON ANIMATION INTERFACE
-- ============================================================================

function AnimationSystem.createWeaponAnimator(weapon: any, weaponModel: Model): any
	local animator = {
		weapon = weapon,
		model = weaponModel,
		baseCFrame = weaponModel:GetPivot(),
		currentOffset = CFrame.new(),
		
		-- Animation states
		recoilOffset = CFrame.new(),
		swayOffset = CFrame.new(),
		reloadOffset = CFrame.new(),
		inspectionOffset = CFrame.new(),
		
		-- Animation settings (can be overridden per weapon)
		settings = weapon.animationSettings or defaultSettings,
		
		-- Active animation tracking
		activeRecoil = nil,
		activeReload = nil,
		activeInspection = nil,
		
		-- Sway state
		swayTime = 0,
		breathingTime = 0,
	}
	
	-- Register animator
	animationCounter += 1
	local animatorId = `weapon_animator_{animationCounter}`
	activeAnimations[animatorId] = animator
	
	return setmetatable(animator, {__index = AnimationSystem})
end

function AnimationSystem.destroyWeaponAnimator(animator: any): ()
	for id, anim in pairs(activeAnimations) do
		if anim == animator then
			activeAnimations[id] = nil
			break
		end
	end
end

-- ============================================================================
-- RECOIL ANIMATIONS
-- ============================================================================

function AnimationSystem:playRecoilAnimation(fireData: any): ()
	if not self.settings.recoil.enabled then return end
	
	-- Stop existing recoil animation
	if self.activeRecoil then
		self.activeRecoil:Cancel()
	end
	
	-- Calculate recoil intensity based on weapon and components
	local baseVertical = self.settings.recoil.verticalIntensity
	local baseHorizontal = self.settings.recoil.horizontalIntensity
	
	-- Apply component modifiers
	if self.weapon.attachedComponents.Stock then
		local stock = self.weapon.attachedComponents.Stock
		baseVertical *= (1 - (stock.recoilReduction or 0))
	end
	
	if self.weapon.attachedComponents.Grip then
		local grip = self.weapon.attachedComponents.Grip
		baseHorizontal *= (1 - (grip.recoilReduction or 0))
	end
	
	-- Add randomness
	local randomFactor = self.settings.recoil.randomness
	local verticalRecoil = baseVertical * (1 + (math.random() - 0.5) * randomFactor)
	local horizontalRecoil = baseHorizontal * (math.random() - 0.5) * 2
	
	-- Create recoil CFrame
	local recoilCFrame = CFrame.Angles(
		math.rad(-verticalRecoil * 10), -- Pitch up
		math.rad(horizontalRecoil * 5), -- Yaw left/right
		0 -- No roll
	) * CFrame.new(0, 0, verticalRecoil * 0.1) -- Slight backward movement
	
	-- Animate recoil
	local recoilTween = TweenService:Create(
		{offset = CFrame.new()},
		TweenInfo.new(
			self.settings.recoil.duration,
			Enum.EasingStyle.Quart,
			Enum.EasingDirection.Out
		),
		{offset = recoilCFrame}
	)
	
	-- Recovery animation
	local recoveryTween = TweenService:Create(
		{offset = recoilCFrame},
		TweenInfo.new(
			self.settings.recoil.recoveryTime,
			Enum.EasingStyle.Quart,
			Enum.EasingDirection.Out
		),
		{offset = CFrame.new()}
	)
	
	-- Update recoil offset during animation
	local connection
	connection = recoilTween:GetPropertyChangedSignal("PlaybackState"):Connect(function()
		if recoilTween.PlaybackState == Enum.PlaybackState.Playing then
			-- Update during recoil
			RunService.Heartbeat:Connect(function()
				if recoilTween.PlaybackState == Enum.PlaybackState.Playing then
					-- Interpolate recoil offset
					local progress = recoilTween:GetNormalizedTime()
					self.recoilOffset = CFrame.new():Lerp(recoilCFrame, progress)
				end
			end)
		elseif recoilTween.PlaybackState == Enum.PlaybackState.Completed then
			-- Start recovery
			recoveryTween:Play()
			
			RunService.Heartbeat:Connect(function()
				if recoveryTween.PlaybackState == Enum.PlaybackState.Playing then
					local progress = recoveryTween:GetNormalizedTime()
					self.recoilOffset = recoilCFrame:Lerp(CFrame.new(), progress)
				elseif recoveryTween.PlaybackState == Enum.PlaybackState.Completed then
					self.recoilOffset = CFrame.new()
					connection:Disconnect()
				end
			end)
		end
	end)
	
	self.activeRecoil = recoilTween
	recoilTween:Play()
end

-- ============================================================================
-- WEAPON SWAY ANIMATIONS
-- ============================================================================

function AnimationSystem:updateWeaponSway(deltaTime: number): ()
	if not self.settings.sway.enabled then return end
	
	self.swayTime += deltaTime * self.settings.sway.frequency
	self.breathingTime += deltaTime * 0.5 -- Slower breathing
	
	-- Calculate sway offsets
	local swayX = math.sin(self.swayTime) * self.settings.sway.intensity
	local swayY = math.cos(self.swayTime * 0.7) * self.settings.sway.intensity * 0.5
	
	-- Add breathing effect
	if self.settings.sway.breathingEffect then
		local breathingOffset = math.sin(self.breathingTime) * 0.01
		swayY += breathingOffset
	end
	
	-- Create sway CFrame
	self.swayOffset = CFrame.Angles(
		math.rad(swayY * 2), -- Pitch
		math.rad(swayX * 3), -- Yaw
		math.rad(swayX * 1)  -- Roll
	) * CFrame.new(swayX * 0.02, swayY * 0.02, 0)
end

-- ============================================================================
-- RELOAD ANIMATIONS
-- ============================================================================

function AnimationSystem:playReloadAnimation(reloadType: string): ()
	if not self.settings.reload.enabled then return end
	
	-- Stop existing reload animation
	if self.activeReload then
		self.activeReload:Cancel()
	end
	
	-- Create reload sequence
	local reloadSequence = {}
	
	if reloadType == "empty" then
		-- Empty reload: magazine out -> magazine in -> bolt release
		table.insert(reloadSequence, {
			name = "magazine_out",
			time = self.settings.reload.magazineDropTime,
			cframe = CFrame.Angles(math.rad(15), 0, 0) * CFrame.new(0, -0.1, 0),
			easing = Enum.EasingStyle.Quart,
		})
		
		table.insert(reloadSequence, {
			name = "magazine_in",
			time = self.settings.reload.magazineInsertTime,
			cframe = CFrame.Angles(math.rad(-10), 0, 0) * CFrame.new(0, 0.05, 0),
			easing = Enum.EasingStyle.Back,
		})
		
		table.insert(reloadSequence, {
			name = "bolt_release",
			time = self.settings.reload.boltReleaseTime,
			cframe = CFrame.Angles(math.rad(5), 0, 0) * CFrame.new(0, 0, -0.02),
			easing = Enum.EasingStyle.Bounce,
		})
	else
		-- Tactical reload: magazine out -> magazine in
		table.insert(reloadSequence, {
			name = "magazine_out",
			time = self.settings.reload.magazineDropTime * 0.7,
			cframe = CFrame.Angles(math.rad(10), 0, 0) * CFrame.new(0, -0.05, 0),
			easing = Enum.EasingStyle.Quad,
		})
		
		table.insert(reloadSequence, {
			name = "magazine_in",
			time = self.settings.reload.magazineInsertTime * 0.8,
			cframe = CFrame.Angles(math.rad(-5), 0, 0) * CFrame.new(0, 0.03, 0),
			easing = Enum.EasingStyle.Back,
		})
	end
	
	-- Play reload sequence
	self:_playAnimationSequence(reloadSequence, function()
		self.reloadOffset = CFrame.new()
		self.activeReload = nil
	end)
end

function AnimationSystem:_playAnimationSequence(sequence: {any}, onComplete: () -> ()): ()
	local currentStep = 1
	
	local function playNextStep()
		if currentStep > #sequence then
			onComplete()
			return
		end
		
		local step = sequence[currentStep]
		local tween = TweenService:Create(
			{offset = self.reloadOffset},
			TweenInfo.new(
				step.time,
				step.easing or Enum.EasingStyle.Quad,
				Enum.EasingDirection.Out
			),
			{offset = step.cframe}
		)
		
		-- Update reload offset during animation
		local connection
		connection = RunService.Heartbeat:Connect(function()
			if tween.PlaybackState == Enum.PlaybackState.Playing then
				local progress = tween:GetNormalizedTime()
				self.reloadOffset = self.reloadOffset:Lerp(step.cframe, progress * self.settings.reload.smoothing)
			elseif tween.PlaybackState == Enum.PlaybackState.Completed then
				connection:Disconnect()
				currentStep += 1
				playNextStep()
			end
		end)
		
		tween:Play()
		self.activeReload = tween
		
		-- Fire animation events
		if step.name == "magazine_out" then
			self.weapon:fireEvent("animation_magazine_out")
		elseif step.name == "magazine_in" then
			self.weapon:fireEvent("animation_magazine_in")
		elseif step.name == "bolt_release" then
			self.weapon:fireEvent("animation_bolt_release")
		end
	end
	
	playNextStep()
end

-- ============================================================================
-- INSPECTION ANIMATIONS
-- ============================================================================

function AnimationSystem:playInspectionAnimation(): ()
	if not self.settings.inspection.enabled then return end
	
	-- Stop existing inspection
	if self.activeInspection then
		self.activeInspection:Cancel()
	end
	
	-- Create inspection rotation
	local inspectionCFrame = CFrame.Angles(
		math.rad(self.settings.inspection.rotationAmount * 0.3),
		math.rad(self.settings.inspection.rotationAmount),
		math.rad(self.settings.inspection.rotationAmount * 0.2)
	)
	
	-- Animate inspection
	local inspectionTween = TweenService:Create(
		{offset = CFrame.new()},
		TweenInfo.new(
			self.settings.inspection.duration,
			Enum.EasingStyle.Sine,
			Enum.EasingDirection.InOut,
			0, -- No repeat
			true -- Reverse
		),
		{offset = inspectionCFrame}
	)
	
	-- Update inspection offset
	local connection
	connection = RunService.Heartbeat:Connect(function()
		if inspectionTween.PlaybackState == Enum.PlaybackState.Playing then
			local progress = inspectionTween:GetNormalizedTime()
			if progress <= 0.5 then
				-- First half: rotate to inspection position
				self.inspectionOffset = CFrame.new():Lerp(inspectionCFrame, progress * 2)
			else
				-- Second half: rotate back
				self.inspectionOffset = inspectionCFrame:Lerp(CFrame.new(), (progress - 0.5) * 2)
			end
		elseif inspectionTween.PlaybackState == Enum.PlaybackState.Completed then
			self.inspectionOffset = CFrame.new()
			self.activeInspection = nil
			connection:Disconnect()
		end
	end)
	
	self.activeInspection = inspectionTween
	inspectionTween:Play()
end

-- ============================================================================
-- ANIMATION UPDATE SYSTEM
-- ============================================================================

function AnimationSystem._updateAnimations(): ()
	local deltaTime = RunService.Heartbeat:Wait()
	
	for _, animator in pairs(activeAnimations) do
		-- Update weapon sway
		animator:updateWeaponSway(deltaTime)
		
		-- Combine all animation offsets
		animator.currentOffset = animator.recoilOffset 
			* animator.swayOffset 
			* animator.reloadOffset 
			* animator.inspectionOffset
		
		-- Apply to weapon model
		if animator.model and animator.model.Parent then
			animator.model:PivotTo(animator.baseCFrame * animator.currentOffset)
		end
	end
end

-- ============================================================================
-- ANIMATION SETTINGS
-- ============================================================================

function AnimationSystem.setGlobalAnimationSettings(settings: {[string]: any}): ()
	for category, categorySettings in pairs(settings) do
		if defaultSettings[category] then
			for setting, value in pairs(categorySettings) do
				defaultSettings[category][setting] = value
			end
		end
	end
	
	print("[AnimationSystem] Global animation settings updated")
end

function AnimationSystem.getDefaultSettings(): {[string]: any}
	return defaultSettings
end

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

function AnimationSystem:setAnimationSettings(settings: {[string]: any}): ()
	for category, categorySettings in pairs(settings) do
		if self.settings[category] then
			for setting, value in pairs(categorySettings) do
				self.settings[category][setting] = value
			end
		end
	end
end

function AnimationSystem:stopAllAnimations(): ()
	if self.activeRecoil then
		self.activeRecoil:Cancel()
		self.activeRecoil = nil
	end
	
	if self.activeReload then
		self.activeReload:Cancel()
		self.activeReload = nil
	end
	
	if self.activeInspection then
		self.activeInspection:Cancel()
		self.activeInspection = nil
	end
	
	-- Reset all offsets
	self.recoilOffset = CFrame.new()
	self.swayOffset = CFrame.new()
	self.reloadOffset = CFrame.new()
	self.inspectionOffset = CFrame.new()
	self.currentOffset = CFrame.new()
end

return AnimationSystem
