--!strict
--[[
	Gun System Framework - Event Bus System

	This module implements a comprehensive event bus system for the GSF.
	It provides event-driven architecture with:

	- Event subscription and publishing
	- Event filtering and prioritization
	- Network replication support
	- Performance monitoring
	- Type-safe event handling
]]

local Types = require(script.Parent.Parent.Types)
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Type imports
type EventType = Types.EventType
type GameEvent = Types.GameEvent
type EventHandler = Types.EventHandler
type EventBus = Types.EventBus
type Player = Types.Player

-- ============================================================================
-- EVENT BUS IMPLEMENTATION
-- ============================================================================

local EventBusImpl = {}
EventBusImpl.__index = EventBusImpl

-- Private storage for the event bus
local subscribers: { [EventType]: { [string]: EventHandler } } = {}
local eventFilters: { [string]: (event: GameEvent) -> boolean } = {}
local eventQueue: { GameEvent } = {}
local processingQueue: { GameEvent } = {}
local subscriptionCounter = 0
local filterCounter = 0

-- Performance tracking
local eventStats = {
  totalEventsPublished = 0,
  totalEventsProcessed = 0,
  averageProcessingTime = 0,
  lastProcessingTime = 0,
  queueSize = 0,
  subscriberCount = 0,
}

-- Network remotes (will be created if needed)
local eventRemotes: { [string]: RemoteEvent } = {}

-- ============================================================================
-- SUBSCRIPTION MANAGEMENT
-- ============================================================================

function EventBusImpl.subscribe(eventType: EventType, handler: EventHandler): string
  assert(eventType, "Event type is required")
  assert(handler, "Event handler is required")
  assert(type(handler) == "function", "Event handler must be a function")

  -- Initialize subscribers table for this event type if needed
  if not subscribers[eventType] then
    subscribers[eventType] = {}
  end

  -- Generate unique subscription ID
  subscriptionCounter += 1
  local subscriptionId = `{eventType}_{subscriptionCounter}_{tick()}`

  -- Store the subscription
  subscribers[eventType][subscriptionId] = handler
  eventStats.subscriberCount += 1

  print(`[EventBus] Subscribed to {eventType} with ID: {subscriptionId}`)
  return subscriptionId
end

function EventBusImpl.unsubscribe(subscriptionId: string): boolean
  -- Find and remove the subscription
  for eventType, eventSubscribers in pairs(subscribers) do
    if eventSubscribers[subscriptionId] then
      eventSubscribers[subscriptionId] = nil
      eventStats.subscriberCount -= 1
      print(`[EventBus] Unsubscribed: {subscriptionId}`)
      return true
    end
  end

  warn(`[EventBus] Subscription not found: {subscriptionId}`)
  return false
end

function EventBusImpl.getSubscriberCount(eventType: EventType): number
  if not subscribers[eventType] then
    return 0
  end

  local count = 0
  for _ in pairs(subscribers[eventType]) do
    count += 1
  end
  return count
end

function EventBusImpl.clearAllSubscriptions(): ()
  subscribers = {}
  eventStats.subscriberCount = 0
  print("[EventBus] Cleared all subscriptions")
end

-- ============================================================================
-- EVENT PUBLISHING
-- ============================================================================

function EventBusImpl.publish(event: GameEvent): ()
  assert(event, "Event is required")
  assert(event.type, "Event type is required")

  -- Validate event structure
  if not EventBusImpl._validateEvent(event) then
    error(`Invalid event structure: {event.type}`)
  end

  -- Apply filters
  if not EventBusImpl._passesFilters(event) then
    return -- Event was filtered out
  end

  -- Add to processing queue
  table.insert(eventQueue, event)
  eventStats.totalEventsPublished += 1
  eventStats.queueSize = #eventQueue

  -- Handle network replication
  if event.replicateToClients and RunService:IsServer() then
    EventBusImpl._replicateToClients(event)
  elseif event.replicateToServer and RunService:IsClient() then
    EventBusImpl._replicateToServer(event)
  end
end

function EventBusImpl.publishLocal(event: GameEvent): ()
  -- Publish event only locally (no network replication)
  local localEvent = table.clone(event)
  localEvent.replicateToClients = false
  localEvent.replicateToServer = false

  EventBusImpl.publish(localEvent)
end

function EventBusImpl.publishToPlayer(event: GameEvent, player: Player): ()
  if not RunService:IsServer() then
    warn("[EventBus] publishToPlayer can only be called on the server")
    return
  end

  -- Set target player and replicate
  local targetedEvent = table.clone(event)
  targetedEvent.targetPlayers = { player }
  targetedEvent.replicateToClients = true

  EventBusImpl.publish(targetedEvent)
end

function EventBusImpl.publishToPlayers(event: GameEvent, players: { Player }): ()
  if not RunService:IsServer() then
    warn("[EventBus] publishToPlayers can only be called on the server")
    return
  end

  -- Set target players and replicate
  local targetedEvent = table.clone(event)
  targetedEvent.targetPlayers = players
  targetedEvent.replicateToClients = true

  EventBusImpl.publish(targetedEvent)
end

-- ============================================================================
-- EVENT FILTERING
-- ============================================================================

function EventBusImpl.addFilter(filter: (event: GameEvent) -> boolean): string
  assert(filter, "Filter function is required")
  assert(type(filter) == "function", "Filter must be a function")

  filterCounter += 1
  local filterId = `filter_{filterCounter}_{tick()}`

  eventFilters[filterId] = filter
  print(`[EventBus] Added event filter: {filterId}`)
  return filterId
end

function EventBusImpl.removeFilter(filterId: string): boolean
  if eventFilters[filterId] then
    eventFilters[filterId] = nil
    print(`[EventBus] Removed event filter: {filterId}`)
    return true
  end

  warn(`[EventBus] Filter not found: {filterId}`)
  return false
end

-- ============================================================================
-- EVENT PROCESSING
-- ============================================================================

function EventBusImpl._processEvents(): ()
  if #eventQueue == 0 then
    return
  end

  local startTime = tick()

  -- Move events to processing queue
  processingQueue = eventQueue
  eventQueue = {}

  -- Process each event
  for _, event in ipairs(processingQueue) do
    EventBusImpl._processEvent(event)
    eventStats.totalEventsProcessed += 1
  end

  -- Clear processing queue
  processingQueue = {}

  -- Update performance stats
  local processingTime = tick() - startTime
  eventStats.lastProcessingTime = processingTime
  eventStats.averageProcessingTime = (eventStats.averageProcessingTime + processingTime) / 2
  eventStats.queueSize = #eventQueue
end

function EventBusImpl._processEvent(event: GameEvent): ()
  local eventSubscribers = subscribers[event.type]
  if not eventSubscribers then
    return -- No subscribers for this event type
  end

  -- Call all subscribers
  for subscriptionId, handler in pairs(eventSubscribers) do
    local success, errorMessage = pcall(handler, event)
    if not success then
      warn(`[EventBus] Error in event handler {subscriptionId}: {errorMessage}`)
    end
  end
end

-- ============================================================================
-- VALIDATION AND UTILITY
-- ============================================================================

function EventBusImpl._validateEvent(event: GameEvent): boolean
  -- Check required fields
  if type(event.type) ~= "string" or event.type == "" then
    return false
  end

  if type(event.timestamp) ~= "number" then
    return false
  end

  if type(event.source) ~= "string" or event.source == "" then
    return false
  end

  if type(event.data) ~= "table" then
    return false
  end

  if type(event.priority) ~= "number" then
    return false
  end

  -- Check boolean fields
  if type(event.replicateToClients) ~= "boolean" then
    return false
  end

  if type(event.replicateToServer) ~= "boolean" then
    return false
  end

  return true
end

function EventBusImpl._passesFilters(event: GameEvent): boolean
  for filterId, filter in pairs(eventFilters) do
    local success, result = pcall(filter, event)
    if not success then
      warn(`[EventBus] Error in event filter {filterId}: {result}`)
      continue
    end

    if not result then
      return false -- Event was filtered out
    end
  end

  return true
end

-- ============================================================================
-- NETWORK REPLICATION
-- ============================================================================

function EventBusImpl._replicateToClients(event: GameEvent): ()
  local remoteName = `GSF_Event_{event.type}`
  local remote = eventRemotes[remoteName]

  if not remote then
    -- Create remote event if it doesn't exist
    remote = Instance.new("RemoteEvent")
    remote.Name = remoteName
    remote.Parent = ReplicatedStorage
    eventRemotes[remoteName] = remote
  end

  if event.targetPlayers then
    -- Send to specific players
    for _, player in ipairs(event.targetPlayers) do
      remote:FireClient(player, event)
    end
  else
    -- Send to all clients
    remote:FireAllClients(event)
  end
end

function EventBusImpl._replicateToServer(event: GameEvent): ()
  local remoteName = `GSF_Event_{event.type}`
  local remote = eventRemotes[remoteName]

  if not remote then
    -- Wait for remote to be created by server
    remote = ReplicatedStorage:WaitForChild(remoteName, 5)
    if remote then
      eventRemotes[remoteName] = remote
    else
      warn(`[EventBus] Remote event not found: {remoteName}`)
      return
    end
  end

  remote:FireServer(event)
end

function EventBusImpl._setupNetworkHandlers(): ()
  -- Set up handlers for incoming network events
  for eventType in pairs(subscribers) do
    local remoteName = `GSF_Event_{eventType}`
    local remote = ReplicatedStorage:FindFirstChild(remoteName)

    if remote and remote:IsA("RemoteEvent") then
      if RunService:IsServer() then
        remote.OnServerEvent:Connect(function(player, event)
          -- Validate that the event came from the correct player
          event.source = `client_{player.UserId}`
          EventBusImpl.publishLocal(event)
        end)
      else
        remote.OnClientEvent:Connect(function(event)
          EventBusImpl.publishLocal(event)
        end)
      end
    end
  end
end

-- ============================================================================
-- PERFORMANCE MONITORING
-- ============================================================================

function EventBusImpl.getStats(): { [string]: any }
  return {
    totalEventsPublished = eventStats.totalEventsPublished,
    totalEventsProcessed = eventStats.totalEventsProcessed,
    averageProcessingTime = eventStats.averageProcessingTime,
    lastProcessingTime = eventStats.lastProcessingTime,
    queueSize = eventStats.queueSize,
    subscriberCount = eventStats.subscriberCount,
    filterCount = filterCounter,
    memoryUsage = collectgarbage("count"),
  }
end

function EventBusImpl.resetStats(): ()
  eventStats = {
    totalEventsPublished = 0,
    totalEventsProcessed = 0,
    averageProcessingTime = 0,
    lastProcessingTime = 0,
    queueSize = #eventQueue,
    subscriberCount = eventStats.subscriberCount, -- Keep subscriber count
  }
  print("[EventBus] Performance stats reset")
end

-- ============================================================================
-- INITIALIZATION AND CLEANUP
-- ============================================================================

function EventBusImpl.initialize(): ()
  -- Set up event processing loop
  RunService.Heartbeat:Connect(EventBusImpl._processEvents)

  -- Set up network handlers
  EventBusImpl._setupNetworkHandlers()

  print("[EventBus] Event bus initialized")
end

function EventBusImpl.cleanup(): ()
  -- Clear all data
  subscribers = {}
  eventFilters = {}
  eventQueue = {}
  processingQueue = {}
  eventRemotes = {}

  -- Reset counters
  subscriptionCounter = 0
  filterCounter = 0

  -- Reset stats
  EventBusImpl.resetStats()

  print("[EventBus] Event bus cleanup completed")
end

-- ============================================================================
-- MODULE EXPORT
-- ============================================================================

return EventBusImpl
